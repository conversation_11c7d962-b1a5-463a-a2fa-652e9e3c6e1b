import websockets
import asyncio
import json
import os
import time
import hmac
import hashlib
import logging
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class MarketDataPoint:
    """Represents a single market data point"""
    symbol: str
    timestamp: datetime
    price: float
    volume: float
    bid: Optional[float] = None
    ask: Optional[float] = None
    high_24h: Optional[float] = None
    low_24h: Optional[float] = None
    change_24h: Optional[float] = None

class MarketDataAggregator:
    """
    Enterprise-grade market data aggregator for real-time trading
    Aggregates data from multiple sources with advanced analytics
    """

    def __init__(self):
        self.data_sources = {}
        self.subscribers = []
        self.cache = {}
        self.analytics_enabled = True
        self.logger = logging.getLogger(__name__)

    def add_data_source(self, name: str, source):
        """Add a data source to the aggregator"""
        self.data_sources[name] = source
        self.logger.info(f"Added data source: {name}")

    def subscribe(self, callback: Callable):
        """Subscribe to aggregated data updates"""
        self.subscribers.append(callback)

    def unsubscribe(self, callback: Callable):
        """Unsubscribe from data updates"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)

    async def aggregate_data(self, symbol: str) -> MarketDataPoint:
        """Aggregate data from all sources for a symbol"""
        aggregated_data = {}
        source_count = 0

        for source_name, source in self.data_sources.items():
            try:
                data = await source.get_ticker(symbol)
                if data:
                    aggregated_data[source_name] = data
                    source_count += 1
            except Exception as e:
                self.logger.warning(f"Failed to get data from {source_name}: {e}")

        if source_count == 0:
            raise ValueError(f"No data available for symbol {symbol}")

        # Calculate weighted average price
        total_volume = sum(data.get('volume', 0) for data in aggregated_data.values())
        if total_volume > 0:
            weighted_price = sum(
                data.get('price', 0) * data.get('volume', 0)
                for data in aggregated_data.values()
            ) / total_volume
        else:
            weighted_price = np.mean([data.get('price', 0) for data in aggregated_data.values()])

        # Create aggregated market data point
        market_data = MarketDataPoint(
            symbol=symbol,
            timestamp=datetime.now(),
            price=weighted_price,
            volume=total_volume,
            bid=np.mean([data.get('bid', 0) for data in aggregated_data.values() if data.get('bid')]),
            ask=np.mean([data.get('ask', 0) for data in aggregated_data.values() if data.get('ask')]),
            high_24h=max([data.get('high_24h', 0) for data in aggregated_data.values() if data.get('high_24h')]),
            low_24h=min([data.get('low_24h', float('inf')) for data in aggregated_data.values() if data.get('low_24h')])
        )

        # Cache the data
        self.cache[symbol] = market_data

        # Notify subscribers
        for callback in self.subscribers:
            try:
                await callback(market_data)
            except Exception as e:
                self.logger.error(f"Error notifying subscriber: {e}")

        return market_data

    def get_cached_data(self, symbol: str) -> Optional[MarketDataPoint]:
        """Get cached data for a symbol"""
        return self.cache.get(symbol)

    async def get_multiple_symbols(self, symbols: List[str]) -> Dict[str, MarketDataPoint]:
        """Get aggregated data for multiple symbols"""
        results = {}
        tasks = [self.aggregate_data(symbol) for symbol in symbols]

        try:
            data_points = await asyncio.gather(*tasks, return_exceptions=True)
            for symbol, data_point in zip(symbols, data_points):
                if isinstance(data_point, Exception):
                    self.logger.error(f"Error getting data for {symbol}: {data_point}")
                else:
                    results[symbol] = data_point
        except Exception as e:
            self.logger.error(f"Error in batch data retrieval: {e}")

        return results

class MarketDataStreamer:
    """
    Real-time market data streaming with authentication and subscription management
    """

    def __init__(self, symbols: List[str], url: str = None):
        self.symbols = symbols
        self.url = url or 'wss://stream.bybit.com/v5/public/spot'
        self.api_key = os.getenv('BYBIT_API_KEY')
        self.api_secret = os.getenv('BYBIT_API_SECRET')
        self.subscribers = []
        self.is_streaming = False
        self.logger = logging.getLogger(__name__)

    async def _authenticate(self, ws):
        """Authenticate with the WebSocket API"""
        if not self.api_key or not self.api_secret:
            self.logger.warning("No API credentials provided for authentication")
            return

        timestamp = str(int(time.time() * 1000))
        signature = hmac.new(
            self.api_secret.encode(),
            timestamp.encode(),
            hashlib.sha256
        ).hexdigest()

        auth_msg = {
            'op': 'auth',
            'args': [self.api_key, timestamp, signature]
        }
        await ws.send(json.dumps(auth_msg))
        self.logger.info("Authentication message sent")

    async def _subscribe(self, ws, symbol: str):
        """Subscribe to ticker data for a symbol"""
        subscribe_msg = {
            'op': 'subscribe',
            'args': [f'tickers.{symbol}']
        }
        await ws.send(json.dumps(subscribe_msg))
        self.logger.info(f"Subscribed to {symbol}")

    async def _listen(self, ws):
        """Listen for incoming data"""
        while self.is_streaming:
            try:
                data = await ws.recv()
                processed_data = self.process_data(json.loads(data))
                if processed_data:
                    await self.notify_subscribers(processed_data)
            except websockets.exceptions.ConnectionClosed:
                self.logger.warning("WebSocket connection closed")
                break
            except Exception as e:
                self.logger.error(f"Error in data listening: {e}")

    async def stream_data(self):
        """Start streaming data for all symbols"""
        self.is_streaming = True
        try:
            async with websockets.connect(self.url) as ws:
                await self._authenticate(ws)

                for symbol in self.symbols:
                    await self._subscribe(ws, symbol)

                await self._listen(ws)
        except Exception as e:
            self.logger.error(f"Error in data streaming: {e}")
        finally:
            self.is_streaming = False

    def process_data(self, raw_data: dict) -> Optional[MarketDataPoint]:
        """Process raw WebSocket data into MarketDataPoint"""
        try:
            if raw_data.get('topic', '').startswith('tickers.'):
                data = raw_data.get('data', {})
                if data:
                    return MarketDataPoint(
                        symbol=data.get('symbol', ''),
                        timestamp=datetime.fromtimestamp(int(data.get('ts', 0)) / 1000),
                        price=float(data.get('lastPrice', 0)),
                        volume=float(data.get('volume24h', 0)),
                        bid=float(data.get('bid1Price', 0)) if data.get('bid1Price') else None,
                        ask=float(data.get('ask1Price', 0)) if data.get('ask1Price') else None,
                        high_24h=float(data.get('highPrice24h', 0)) if data.get('highPrice24h') else None,
                        low_24h=float(data.get('lowPrice24h', 0)) if data.get('lowPrice24h') else None,
                        change_24h=float(data.get('price24hPcnt', 0)) if data.get('price24hPcnt') else None
                    )
        except Exception as e:
            self.logger.error(f"Error processing data: {e}")
        return None

    def subscribe(self, callback: Callable):
        """Subscribe to data updates"""
        self.subscribers.append(callback)

    async def notify_subscribers(self, data: MarketDataPoint):
        """Notify all subscribers of new data"""
        for callback in self.subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception as e:
                self.logger.error(f"Error notifying subscriber: {e}")

    def stop_streaming(self):
        """Stop the data streaming"""
        self.is_streaming = False

class AlphaFactory:
    """
    Advanced alpha generation factory for quantitative trading strategies
    Generates alpha signals from market data using multiple methodologies
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.alpha_cache = {}
        self.lookback_periods = [5, 10, 20, 50]

    def generate_alpha(self, data: Dict[str, Any]) -> float:
        """Generate alpha signal from market data"""
        try:
            # Multi-factor alpha generation
            momentum_alpha = self._calculate_momentum_alpha(data)
            mean_reversion_alpha = self._calculate_mean_reversion_alpha(data)
            volume_alpha = self._calculate_volume_alpha(data)
            volatility_alpha = self._calculate_volatility_alpha(data)

            # Weighted combination of alpha factors
            combined_alpha = (
                momentum_alpha * 0.25 +
                mean_reversion_alpha * 0.25 +
                volume_alpha * 0.30 +
                volatility_alpha * 0.20
            )

            return np.clip(combined_alpha, -1.0, 1.0)

        except Exception as e:
            self.logger.error(f"Error generating alpha: {e}")
            return 0.0

    def _calculate_momentum_alpha(self, data: Dict[str, Any]) -> float:
        """Calculate momentum-based alpha signal"""
        try:
            prices = data.get('prices', [])
            if len(prices) < 10:
                return 0.0

            # Short-term momentum
            short_momentum = (prices[-1] - prices[-5]) / prices[-5] if len(prices) >= 5 else 0

            # Medium-term momentum
            medium_momentum = (prices[-1] - prices[-10]) / prices[-10] if len(prices) >= 10 else 0

            # Combine momentum signals
            momentum_signal = (short_momentum * 0.6 + medium_momentum * 0.4)

            return momentum_signal

        except Exception as e:
            self.logger.error(f"Error calculating momentum alpha: {e}")
            return 0.0

    def _calculate_mean_reversion_alpha(self, data: Dict[str, Any]) -> float:
        """Calculate mean reversion alpha signal"""
        try:
            prices = data.get('prices', [])
            if len(prices) < 20:
                return 0.0

            # Calculate moving average
            ma_20 = np.mean(prices[-20:])
            current_price = prices[-1]

            # Mean reversion signal (negative when price is above MA)
            deviation = (current_price - ma_20) / ma_20
            mean_reversion_signal = -deviation  # Negative for mean reversion

            return mean_reversion_signal

        except Exception as e:
            self.logger.error(f"Error calculating mean reversion alpha: {e}")
            return 0.0

    def _calculate_volume_alpha(self, data: Dict[str, Any]) -> float:
        """Calculate volume-based alpha signal"""
        try:
            volumes = data.get('volumes', [])
            prices = data.get('prices', [])

            if len(volumes) < 10 or len(prices) < 10:
                return 0.0

            # Volume-weighted price change
            recent_volume = np.mean(volumes[-5:])
            avg_volume = np.mean(volumes[-20:]) if len(volumes) >= 20 else np.mean(volumes)

            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0
            price_change = (prices[-1] - prices[-5]) / prices[-5] if len(prices) >= 5 else 0

            # Volume confirmation signal
            volume_alpha = price_change * min(volume_ratio, 2.0)  # Cap volume ratio

            return volume_alpha

        except Exception as e:
            self.logger.error(f"Error calculating volume alpha: {e}")
            return 0.0

    def _calculate_volatility_alpha(self, data: Dict[str, Any]) -> float:
        """Calculate volatility-based alpha signal"""
        try:
            prices = data.get('prices', [])
            if len(prices) < 20:
                return 0.0

            # Calculate returns
            returns = np.diff(prices) / prices[:-1]

            # Recent volatility vs historical volatility
            recent_vol = np.std(returns[-10:]) if len(returns) >= 10 else 0
            historical_vol = np.std(returns[-20:]) if len(returns) >= 20 else recent_vol

            # Volatility regime signal
            vol_ratio = recent_vol / historical_vol if historical_vol > 0 else 1.0

            # Lower volatility might indicate trend continuation
            volatility_alpha = -0.5 * (vol_ratio - 1.0)

            return volatility_alpha

        except Exception as e:
            self.logger.error(f"Error calculating volatility alpha: {e}")
            return 0.0

    def generate_multi_timeframe_alpha(self, data: Dict[str, Any]) -> Dict[str, float]:
        """Generate alpha signals for multiple timeframes"""
        alphas = {}

        for period in self.lookback_periods:
            try:
                # Extract data for specific lookback period
                period_data = {
                    'prices': data.get('prices', [])[-period:],
                    'volumes': data.get('volumes', [])[-period:],
                }

                alpha = self.generate_alpha(period_data)
                alphas[f'alpha_{period}'] = alpha

            except Exception as e:
                self.logger.error(f"Error generating alpha for period {period}: {e}")
                alphas[f'alpha_{period}'] = 0.0

        return alphas

    def get_alpha_strength(self, alpha_value: float) -> str:
        """Classify alpha signal strength"""
        abs_alpha = abs(alpha_value)

        if abs_alpha >= 0.7:
            return "STRONG"
        elif abs_alpha >= 0.4:
            return "MODERATE"
        elif abs_alpha >= 0.2:
            return "WEAK"
        else:
            return "NEUTRAL"
