"""
Data Feed Monitoring System
Provides comprehensive monitoring, metrics collection, and alerting for data feeds
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import logging
import statistics
import json

logger = logging.getLogger(__name__)

@dataclass
class MetricPoint:
    """Single metric data point"""
    timestamp: datetime
    value: float
    tags: Dict[str, str] = field(default_factory=dict)

@dataclass
class AlertRule:
    """Alert rule configuration"""
    name: str
    metric: str
    condition: str  # 'gt', 'lt', 'eq', 'gte', 'lte'
    threshold: float
    duration: int  # seconds
    callback: Optional[Callable] = None
    enabled: bool = True

class MetricsCollector:
    """
    High-performance metrics collector with aggregation and alerting
    """
    
    def __init__(self, max_points: int = 10000):
        self.max_points = max_points
        self.metrics = defaultdict(lambda: deque(maxlen=max_points))
        self.counters = defaultdict(int)
        self.gauges = defaultdict(float)
        self.timers = defaultdict(list)
        self.lock = threading.RLock()
        self.alert_rules = {}
        self.alert_states = {}
        
    def record_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record a metric value"""
        with self.lock:
            metric_point = MetricPoint(
                timestamp=datetime.now(),
                value=value,
                tags=tags or {}
            )
            self.metrics[name].append(metric_point)
            self._check_alerts(name, value)
            
    def increment_counter(self, name: str, value: int = 1, tags: Optional[Dict[str, str]] = None) -> None:
        """Increment a counter metric"""
        with self.lock:
            self.counters[name] += value
            self.record_metric(f"{name}_counter", self.counters[name], tags)
            
    def set_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Set a gauge metric"""
        with self.lock:
            self.gauges[name] = value
            self.record_metric(f"{name}_gauge", value, tags)
            
    def record_timer(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record a timer metric"""
        with self.lock:
            self.timers[name].append(duration)
            # Keep only recent timer values
            if len(self.timers[name]) > 1000:
                self.timers[name] = self.timers[name][-1000:]
            self.record_metric(f"{name}_timer", duration, tags)
            
    def get_metric_stats(self, name: str, window_minutes: int = 5) -> Dict[str, Any]:
        """Get statistics for a metric within time window"""
        with self.lock:
            if name not in self.metrics:
                return {}
                
            cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
            recent_points = [
                point for point in self.metrics[name]
                if point.timestamp >= cutoff_time
            ]
            
            if not recent_points:
                return {}
                
            values = [point.value for point in recent_points]
            
            return {
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'std_dev': statistics.stdev(values) if len(values) > 1 else 0,
                'latest': values[-1],
                'window_minutes': window_minutes
            }
            
    def get_timer_stats(self, name: str) -> Dict[str, Any]:
        """Get timer statistics"""
        with self.lock:
            if name not in self.timers or not self.timers[name]:
                return {}
                
            values = self.timers[name]
            
            return {
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'p95': statistics.quantiles(values, n=20)[18] if len(values) >= 20 else max(values),
                'p99': statistics.quantiles(values, n=100)[98] if len(values) >= 100 else max(values)
            }
            
    def add_alert_rule(self, rule: AlertRule) -> None:
        """Add an alert rule"""
        self.alert_rules[rule.name] = rule
        self.alert_states[rule.name] = {
            'triggered': False,
            'trigger_time': None,
            'last_check': None
        }
        logger.info(f"Added alert rule: {rule.name}")
        
    def _check_alerts(self, metric_name: str, value: float) -> None:
        """Check alert rules for a metric"""
        for rule_name, rule in self.alert_rules.items():
            if rule.metric != metric_name or not rule.enabled:
                continue
                
            state = self.alert_states[rule_name]
            triggered = self._evaluate_condition(value, rule.condition, rule.threshold)
            
            if triggered and not state['triggered']:
                # Alert triggered
                state['triggered'] = True
                state['trigger_time'] = datetime.now()
                if rule.callback:
                    try:
                        rule.callback(rule, metric_name, value)
                    except Exception as e:
                        logger.error(f"Error in alert callback: {e}")
                        
            elif not triggered and state['triggered']:
                # Alert resolved
                state['triggered'] = False
                state['trigger_time'] = None
                
            state['last_check'] = datetime.now()
            
    def _evaluate_condition(self, value: float, condition: str, threshold: float) -> bool:
        """Evaluate alert condition"""
        if condition == 'gt':
            return value > threshold
        elif condition == 'lt':
            return value < threshold
        elif condition == 'gte':
            return value >= threshold
        elif condition == 'lte':
            return value <= threshold
        elif condition == 'eq':
            return abs(value - threshold) < 0.0001
        else:
            return False
            
    def get_all_metrics(self) -> Dict[str, Any]:
        """Get all metrics summary"""
        with self.lock:
            return {
                'counters': dict(self.counters),
                'gauges': dict(self.gauges),
                'metrics_count': {name: len(points) for name, points in self.metrics.items()},
                'timer_counts': {name: len(values) for name, values in self.timers.items()}
            }

class DataFeedMonitor:
    """
    Comprehensive data feed monitoring with health checks and performance tracking
    """
    
    def __init__(self):
        self.metrics = MetricsCollector()
        self.feed_status = {}
        self.health_checks = {}
        self.performance_targets = {}
        self.logger = logging.getLogger(__name__)
        
        # Setup default alert rules
        self._setup_default_alerts()
        
    def register_feed(self, feed_name: str, health_check: Optional[Callable] = None) -> None:
        """Register a data feed for monitoring"""
        self.feed_status[feed_name] = {
            'status': 'unknown',
            'last_update': None,
            'error_count': 0,
            'total_requests': 0,
            'successful_requests': 0
        }
        
        if health_check:
            self.health_checks[feed_name] = health_check
            
        self.logger.info(f"Registered data feed: {feed_name}")
        
    def record_feed_request(self, feed_name: str, success: bool, latency: float) -> None:
        """Record a data feed request"""
        if feed_name not in self.feed_status:
            self.register_feed(feed_name)
            
        status = self.feed_status[feed_name]
        status['total_requests'] += 1
        status['last_update'] = datetime.now()
        
        if success:
            status['successful_requests'] += 1
            status['status'] = 'healthy'
        else:
            status['error_count'] += 1
            status['status'] = 'error'
            
        # Record metrics
        self.metrics.record_timer(f"{feed_name}_latency", latency)
        self.metrics.increment_counter(f"{feed_name}_requests")
        
        if success:
            self.metrics.increment_counter(f"{feed_name}_success")
        else:
            self.metrics.increment_counter(f"{feed_name}_errors")
            
    def get_feed_health(self, feed_name: str) -> Dict[str, Any]:
        """Get health status for a feed"""
        if feed_name not in self.feed_status:
            return {'status': 'unknown'}
            
        status = self.feed_status[feed_name]
        success_rate = 0
        
        if status['total_requests'] > 0:
            success_rate = status['successful_requests'] / status['total_requests']
            
        latency_stats = self.metrics.get_timer_stats(f"{feed_name}_latency")
        
        return {
            'status': status['status'],
            'last_update': status['last_update'],
            'success_rate': success_rate,
            'error_count': status['error_count'],
            'total_requests': status['total_requests'],
            'latency_stats': latency_stats
        }
        
    def run_health_checks(self) -> Dict[str, bool]:
        """Run health checks for all registered feeds"""
        results = {}
        
        for feed_name, health_check in self.health_checks.items():
            try:
                start_time = time.time()
                is_healthy = health_check()
                latency = time.time() - start_time
                
                results[feed_name] = is_healthy
                self.record_feed_request(feed_name, is_healthy, latency)
                
            except Exception as e:
                self.logger.error(f"Health check failed for {feed_name}: {e}")
                results[feed_name] = False
                self.record_feed_request(feed_name, False, 0)
                
        return results
        
    def set_performance_target(self, feed_name: str, max_latency: float, min_success_rate: float) -> None:
        """Set performance targets for a feed"""
        self.performance_targets[feed_name] = {
            'max_latency': max_latency,
            'min_success_rate': min_success_rate
        }
        
        # Add alert rules for performance targets
        self.metrics.add_alert_rule(AlertRule(
            name=f"{feed_name}_latency_alert",
            metric=f"{feed_name}_latency",
            condition='gt',
            threshold=max_latency,
            duration=30
        ))
        
    def _setup_default_alerts(self) -> None:
        """Setup default alert rules"""
        # High error rate alert
        self.metrics.add_alert_rule(AlertRule(
            name="high_error_rate",
            metric="error_rate",
            condition='gt',
            threshold=0.1,  # 10% error rate
            duration=60
        ))
        
        # High latency alert
        self.metrics.add_alert_rule(AlertRule(
            name="high_latency",
            metric="avg_latency",
            condition='gt',
            threshold=5.0,  # 5 seconds
            duration=30
        ))
        
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get comprehensive monitoring summary"""
        return {
            'feeds': {name: self.get_feed_health(name) for name in self.feed_status.keys()},
            'metrics': self.metrics.get_all_metrics(),
            'alert_states': self.metrics.alert_states,
            'timestamp': datetime.now().isoformat()
        }

# Global monitor instance
data_feed_monitor = DataFeedMonitor()
