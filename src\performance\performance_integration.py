#!/usr/bin/env python3
"""
PERFORMANCE INTEGRATION MODULE
Integrates all performance optimizations into the main trading system
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass

# Import all optimization components
from .intelligent_cache_manager import intelligent_cache, smart_cache
from .parallel_processor import parallel_processor, parallel_execute
from .trading_optimizer import TradingPerformanceOptimizer
from .speed_optimizer import speed_optimizer, fast_api_call, fast_neural_inference
from ..exchanges.high_speed_connection_pool import connection_pool
from ..neural.performance_optimizer import InferenceOptimizer, OptimizationConfig

logger = logging.getLogger(__name__)

@dataclass
class PerformanceConfig:
    """Configuration for performance optimizations"""
    enable_intelligent_caching: bool = True
    enable_parallel_processing: bool = True
    enable_connection_pooling: bool = True
    enable_neural_optimization: bool = True
    enable_api_optimization: bool = True
    max_workers: int = 16
    cache_size_mb: float = 512
    target_latency_ms: float = 100
    
class PerformanceIntegrator:
    """OPTIMIZED: Integrates all performance optimizations into the trading system"""
    
    def __init__(self, config: PerformanceConfig = None):
        self.config = config or PerformanceConfig()
        self.initialized = False
        self.optimization_stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'parallel_tasks': 0,
            'api_calls_optimized': 0,
            'neural_inferences': 0
        }
        
        # Component references
        self.trading_optimizer = None
        self.neural_optimizer = None
        
        logger.info("🚀 [INTEGRATOR] Performance integrator initialized")
    
    async def initialize(self) -> bool:
        """Initialize all performance optimization components"""
        if self.initialized:
            return True
        
        logger.info("⚡ [INTEGRATOR] Initializing performance optimizations...")
        
        try:
            # Initialize connection pool
            if self.config.enable_connection_pooling:
                await self._initialize_connection_pool()
            
            # Initialize parallel processing
            if self.config.enable_parallel_processing:
                await self._initialize_parallel_processing()
            
            # Initialize neural optimization
            if self.config.enable_neural_optimization:
                await self._initialize_neural_optimization()
            
            # Initialize trading optimizer
            await self._initialize_trading_optimizer()
            
            self.initialized = True
            logger.info("✅ [INTEGRATOR] All performance optimizations initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATOR] Initialization failed: {e}")
            return False
    
    async def _initialize_connection_pool(self):
        """Initialize high-speed connection pool"""
        logger.info("🔗 [INTEGRATOR] Initializing connection pool...")
        await connection_pool._initialize_session()
        logger.info("✅ [INTEGRATOR] Connection pool ready")
    
    async def _initialize_parallel_processing(self):
        """Initialize parallel processing system"""
        logger.info("🚀 [INTEGRATOR] Initializing parallel processing...")
        await parallel_processor.start()
        logger.info("✅ [INTEGRATOR] Parallel processing ready")
    
    async def _initialize_neural_optimization(self):
        """Initialize neural network optimizations"""
        logger.info("🧠 [INTEGRATOR] Initializing neural optimizations...")
        
        # Load configuration from professional_config.json
        neural_config = OptimizationConfig.from_config_file()
        self.neural_optimizer = InferenceOptimizer(neural_config)
        
        logger.info("✅ [INTEGRATOR] Neural optimizations ready")
    
    async def _initialize_trading_optimizer(self):
        """Initialize trading performance optimizer"""
        logger.info("📈 [INTEGRATOR] Initializing trading optimizer...")
        self.trading_optimizer = TradingPerformanceOptimizer()
        logger.info("✅ [INTEGRATOR] Trading optimizer ready")
    
    # Optimized trading operations
    @smart_cache(category="price", ttl=3.0, priority=3)
    async def get_optimized_price(self, exchange_client, symbol: str) -> float:
        """Get price with intelligent caching and optimization"""
        try:
            start_time = time.time()
            
            # Use optimized price fetching if available
            if hasattr(exchange_client, 'get_price_optimized'):
                price = await exchange_client.get_price_optimized(symbol)
            else:
                price = await exchange_client.get_price(symbol)
            
            execution_time = (time.time() - start_time) * 1000
            
            # Record performance metrics
            self.optimization_stats['api_calls_optimized'] += 1
            
            if execution_time > self.config.target_latency_ms:
                logger.warning(f"⚠️ [INTEGRATOR] Slow price fetch: {execution_time:.1f}ms for {symbol}")
            
            return float(price)
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATOR] Price fetch failed for {symbol}: {e}")
            return 0.0
    
    @smart_cache(category="balance", ttl=2.0, priority=3)
    async def get_optimized_balance(self, exchange_client, currency: str = "USDT") -> float:
        """Get balance with intelligent caching and optimization"""
        try:
            start_time = time.time()
            
            # Use optimized balance fetching if available
            if hasattr(exchange_client, 'get_balance_optimized'):
                balance = await exchange_client.get_balance_optimized(currency)
            else:
                balance = await exchange_client.get_balance(currency)
            
            execution_time = (time.time() - start_time) * 1000
            
            # Record performance metrics
            self.optimization_stats['api_calls_optimized'] += 1
            
            if execution_time > self.config.target_latency_ms:
                logger.warning(f"⚠️ [INTEGRATOR] Slow balance fetch: {execution_time:.1f}ms for {currency}")
            
            return float(balance)
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATOR] Balance fetch failed for {currency}: {e}")
            return 0.0
    
    async def get_multiple_prices_parallel(self, exchange_client, symbols: list) -> Dict[str, float]:
        """Get multiple prices in parallel with optimization"""
        try:
            # Use bulk price fetching if available
            if hasattr(exchange_client, 'get_multiple_prices'):
                return await exchange_client.get_multiple_prices(symbols)
            
            # Fallback to parallel individual requests
            tasks = [
                lambda s=symbol: self.get_optimized_price(exchange_client, s)
                for symbol in symbols
            ]
            
            results = await parallel_execute(
                tasks, 
                task_type="api_call", 
                priority=3, 
                timeout=10.0
            )
            
            self.optimization_stats['parallel_tasks'] += len(tasks)
            
            return {
                symbol: result for symbol, result in zip(symbols, results)
                if result is not None
            }
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATOR] Parallel price fetch failed: {e}")
            return {}
    
    @fast_neural_inference
    async def optimized_neural_inference(self, model, input_data, model_name: str = "default"):
        """Perform optimized neural inference"""
        try:
            start_time = time.time()
            
            if self.neural_optimizer:
                # Use optimized inference pipeline
                result = await self.neural_optimizer.async_inference(model, input_data, model_name)
            else:
                # Fallback to direct inference
                result = await model(input_data)
            
            execution_time = (time.time() - start_time) * 1000
            self.optimization_stats['neural_inferences'] += 1
            
            if execution_time > self.config.target_latency_ms:
                logger.warning(f"⚠️ [INTEGRATOR] Slow neural inference: {execution_time:.1f}ms")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATOR] Neural inference failed: {e}")
            return None
    
    async def execute_trading_pipeline_optimized(self, pipeline_steps: list) -> Dict[str, Any]:
        """Execute trading pipeline with full optimization"""
        try:
            start_time = time.time()
            
            # Categorize steps for optimal execution
            api_steps = [step for step in pipeline_steps if step.get('type') == 'api']
            neural_steps = [step for step in pipeline_steps if step.get('type') == 'neural']
            data_steps = [step for step in pipeline_steps if step.get('type') == 'data']
            
            results = {}
            
            # Execute API steps in parallel
            if api_steps:
                api_tasks = [step['function'] for step in api_steps]
                api_results = await parallel_execute(
                    api_tasks, 
                    task_type="api_call", 
                    priority=3, 
                    timeout=5.0
                )
                for i, step in enumerate(api_steps):
                    results[step['name']] = api_results[i]
            
            # Execute neural steps with optimization
            if neural_steps:
                for step in neural_steps:
                    result = await self.optimized_neural_inference(
                        step['model'], 
                        step['input'], 
                        step['name']
                    )
                    results[step['name']] = result
            
            # Execute data processing steps in parallel
            if data_steps:
                data_tasks = [step['function'] for step in data_steps]
                data_results = await parallel_execute(
                    data_tasks, 
                    task_type="data_processing", 
                    priority=2, 
                    timeout=10.0
                )
                for i, step in enumerate(data_steps):
                    results[step['name']] = data_results[i]
            
            execution_time = (time.time() - start_time) * 1000
            
            logger.info(f"⚡ [INTEGRATOR] Pipeline executed in {execution_time:.1f}ms")
            
            return {
                'results': results,
                'execution_time_ms': execution_time,
                'steps_executed': len(pipeline_steps)
            }
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATOR] Pipeline execution failed: {e}")
            return {'results': {}, 'execution_time_ms': float('inf'), 'error': str(e)}
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        cache_report = intelligent_cache.get_performance_report()
        parallel_report = parallel_processor.get_performance_report()
        
        return {
            'optimization_stats': self.optimization_stats,
            'cache_performance': cache_report,
            'parallel_performance': parallel_report,
            'trading_optimizer': self.trading_optimizer.get_performance_report() if self.trading_optimizer else {},
            'system_status': {
                'initialized': self.initialized,
                'connection_pool_active': connection_pool._session_initialized,
                'parallel_processor_running': parallel_processor.running,
                'neural_optimizer_ready': self.neural_optimizer is not None
            }
        }
    
    async def optimize_trading_system(self, trading_system) -> bool:
        """Apply optimizations to an existing trading system"""
        try:
            logger.info("🔧 [INTEGRATOR] Optimizing trading system...")
            
            # Ensure we're initialized
            if not await self.initialize():
                return False
            
            # Apply optimizations to exchange clients
            if hasattr(trading_system, 'exchange_clients'):
                for name, client in trading_system.exchange_clients.items():
                    await self._optimize_exchange_client(client, name)
            
            # Apply optimizations to neural components
            if hasattr(trading_system, 'neural_models'):
                await self._optimize_neural_models(trading_system.neural_models)
            
            # Apply optimizations to strategy components
            if hasattr(trading_system, 'strategies'):
                await self._optimize_strategies(trading_system.strategies)
            
            logger.info("✅ [INTEGRATOR] Trading system optimization complete")
            return True
            
        except Exception as e:
            logger.error(f"❌ [INTEGRATOR] Trading system optimization failed: {e}")
            return False
    
    async def _optimize_exchange_client(self, client, name: str):
        """Apply optimizations to an exchange client"""
        logger.info(f"🔧 [INTEGRATOR] Optimizing {name} exchange client...")
        
        # Add performance monitoring decorators
        if hasattr(client, 'get_price'):
            original_get_price = client.get_price
            client.get_price = fast_api_call(original_get_price)
        
        if hasattr(client, 'get_balance'):
            original_get_balance = client.get_balance
            client.get_balance = fast_api_call(original_get_balance)
    
    async def _optimize_neural_models(self, neural_models: dict):
        """Apply optimizations to neural models"""
        logger.info("🧠 [INTEGRATOR] Optimizing neural models...")
        
        if self.neural_optimizer:
            for model_name, model in neural_models.items():
                try:
                    # Create sample input for optimization
                    sample_input = model.get_sample_input() if hasattr(model, 'get_sample_input') else None
                    
                    if sample_input is not None:
                        optimized_model = self.neural_optimizer.optimize_model(
                            model, model_name, sample_input
                        )
                        neural_models[model_name] = optimized_model
                        logger.info(f"✅ [INTEGRATOR] Optimized neural model: {model_name}")
                    
                except Exception as e:
                    logger.warning(f"⚠️ [INTEGRATOR] Could not optimize {model_name}: {e}")
    
    async def _optimize_strategies(self, strategies: dict):
        """Apply optimizations to trading strategies"""
        logger.info("📈 [INTEGRATOR] Optimizing trading strategies...")
        
        for strategy_name, strategy in strategies.items():
            try:
                # Add caching to strategy signals
                if hasattr(strategy, 'generate_signal'):
                    original_generate_signal = strategy.generate_signal
                    strategy.generate_signal = smart_cache(
                        category="strategy_signal", 
                        ttl=1.0, 
                        priority=3
                    )(original_generate_signal)
                
                logger.info(f"✅ [INTEGRATOR] Optimized strategy: {strategy_name}")
                
            except Exception as e:
                logger.warning(f"⚠️ [INTEGRATOR] Could not optimize {strategy_name}: {e}")

# Global performance integrator instance
performance_integrator = PerformanceIntegrator()

async def initialize_performance_optimizations() -> bool:
    """Initialize all performance optimizations"""
    return await performance_integrator.initialize()

async def optimize_trading_system(trading_system) -> bool:
    """Apply all optimizations to a trading system"""
    return await performance_integrator.optimize_trading_system(trading_system)

def get_performance_stats() -> Dict[str, Any]:
    """Get comprehensive performance statistics"""
    return performance_integrator.get_performance_stats()

logger.info("⚡ [INTEGRATOR] Performance integration module loaded")
