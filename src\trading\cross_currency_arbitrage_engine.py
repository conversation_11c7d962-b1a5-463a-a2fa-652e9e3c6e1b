"""
Cross-Currency Arbitrage Trading Engine

Advanced arbitrage engine that implements trading strategies for any currency pairs
(BTC/ETH, ETH/SOL, etc.), not just crypto/USDT pairs. Includes triangular arbitrage
and cross-exchange arbitrage opportunities based on professional HFT methodologies.
"""

import asyncio
import logging
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from enum import Enum
import time
import itertools
import math

logger = logging.getLogger(__name__)

class ArbitrageType(Enum):
    """Types of arbitrage strategies"""
    TRIANGULAR = "triangular"          # A->B->C->A within single exchange
    CROSS_EXCHANGE = "cross_exchange"  # Same pair across different exchanges
    CROSS_CURRENCY = "cross_currency"  # Different currency pairs with correlation
    STATISTICAL = "statistical"       # Mean reversion arbitrage

@dataclass
class ArbitrageOpportunity:
    """Represents an arbitrage opportunity"""
    arbitrage_type: ArbitrageType
    exchanges: List[str]
    currency_path: List[str]
    symbol_path: List[str]
    price_path: List[Decimal]
    amounts: List[Decimal]
    expected_profit: Decimal
    profit_percentage: float
    execution_time_estimate: float
    risk_score: float
    confidence: float
    min_capital_required: Decimal

@dataclass
class CurrencyGraph:
    """Graph representation of currency relationships"""
    nodes: Set[str]  # Currencies
    edges: Dict[Tuple[str, str], Dict]  # (from, to) -> {price, exchange, symbol}

class CrossCurrencyArbitrageEngine:
    """
    Advanced cross-currency arbitrage engine implementing professional
    arbitrage strategies used by institutional trading firms
    """
    
    def __init__(self, exchange_clients: Dict[str, Any], config: Dict = None):
        self.exchange_clients = exchange_clients
        self.config = config or {}
        
        # Arbitrage configuration
        self.min_profit_threshold = self.config.get('min_profit_threshold', 0.002)  # 0.2%
        self.max_execution_time = self.config.get('max_execution_time', 5.0)  # 5 seconds
        self.min_capital_requirement = Decimal(str(self.config.get('min_capital', 10.0)))
        
        # Currency graphs for each exchange
        self.currency_graphs = {}
        self.cross_exchange_prices = {}
        
        # Performance tracking
        self.arbitrage_stats = {
            'opportunities_found': 0,
            'opportunities_executed': 0,
            'total_profit': Decimal('0'),
            'success_rate': 0.0
        }
        
        # Risk management
        self.max_position_size = Decimal('1000')  # Maximum position size
        self.correlation_matrix = {}
        
        logger.info("🔄 [ARBITRAGE] Initialized cross-currency arbitrage engine")
    
    async def initialize(self):
        """Initialize the arbitrage engine"""
        try:
            logger.info("🔧 [ARBITRAGE] Initializing arbitrage engine...")
            
            # Build currency graphs for each exchange
            await self.build_currency_graphs()
            
            # Initialize cross-exchange price monitoring
            await self.initialize_cross_exchange_monitoring()
            
            # Calculate currency correlations
            await self.calculate_currency_correlations()
            
            logger.info("✅ [ARBITRAGE] Arbitrage engine initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ [ARBITRAGE] Error initializing arbitrage engine: {e}")
            raise
    
    async def build_currency_graphs(self):
        """Build currency relationship graphs for each exchange"""
        try:
            logger.info("📊 [ARBITRAGE] Building currency graphs...")
            
            for exchange_name, client in self.exchange_clients.items():
                try:
                    graph = CurrencyGraph(nodes=set(), edges={})
                    
                    # Get all available balances to find currencies
                    if hasattr(client, 'get_all_available_balances'):
                        balances = await client.get_all_available_balances()
                        currencies = set(balances.keys())
                        graph.nodes.update(currencies)
                    
                    # Discover trading pairs and build edges - FOCUS ON USDT PAIRS ONLY
                    detected_currencies = list(self.currency_balances.get(exchange_name, {}).keys()) if hasattr(self, 'currency_balances') else []
                    base_currencies = detected_currencies if detected_currencies else ['BTC', 'ETH', 'SOL', 'ADA', 'DOT']

                    # Primary focus: USDT pairs (most liquid on Bybit)
                    primary_quote = 'USDT'

                    # Generate ONLY USDT pairs for detected currencies
                    for base in base_currencies:
                        if base == primary_quote or base in ['USD', 'USDC']:
                            continue

                        symbol = f"{base}{primary_quote}"

                        try:
                            if hasattr(client, 'get_price'):
                                price = client.get_price(symbol)
                                if price and float(price) > 0:
                                    graph.edges[(base, primary_quote)] = {
                                        'price': Decimal(str(price)),
                                        'exchange': exchange_name,
                                        'symbol': symbol,
                                        'last_update': time.time()
                                    }
                                    graph.nodes.add(base)
                                    graph.nodes.add(primary_quote)
                        except Exception:
                            continue

                    # CRITICAL FIX: Only add pairs that actually exist on Bybit
                    # Bybit primarily supports USDT pairs, not direct crypto-to-crypto pairs
                    # Skip BTC pairs as they don't exist on Bybit spot trading
                    logger.info(f"📊 [ARBITRAGE] {exchange_name}: Focusing on USDT pairs only (Bybit doesn't support direct crypto pairs)")
                    
                    self.currency_graphs[exchange_name] = graph
                    
                    logger.info(f"📊 [ARBITRAGE] {exchange_name}: {len(graph.nodes)} currencies, {len(graph.edges)} pairs")
                    
                except Exception as e:
                    logger.warning(f"⚠️ [ARBITRAGE] Error building graph for {exchange_name}: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"❌ [ARBITRAGE] Error building currency graphs: {e}")
    
    async def find_triangular_arbitrage_opportunities(self) -> List[ArbitrageOpportunity]:
        """Find triangular arbitrage opportunities within each exchange"""
        opportunities = []
        
        try:
            logger.info("🔍 [TRIANGULAR] Scanning for triangular arbitrage opportunities...")
            
            for exchange_name, graph in self.currency_graphs.items():
                try:
                    # Find all possible triangular paths
                    currencies = list(graph.nodes)
                    
                    for curr_a, curr_b, curr_c in itertools.permutations(currencies, 3):
                        # Check if all required edges exist
                        edge_ab = (curr_a, curr_b)
                        edge_bc = (curr_b, curr_c)
                        edge_ca = (curr_c, curr_a)
                        
                        if all(edge in graph.edges for edge in [edge_ab, edge_bc, edge_ca]):
                            # Calculate arbitrage profit
                            price_ab = graph.edges[edge_ab]['price']
                            price_bc = graph.edges[edge_bc]['price']
                            price_ca = graph.edges[edge_ca]['price']
                            
                            # Start with 1 unit of curr_a
                            # A -> B: 1 / price_ab
                            # B -> C: (1 / price_ab) / price_bc
                            # C -> A: ((1 / price_ab) / price_bc) / price_ca
                            final_amount = Decimal('1') / (price_ab * price_bc * price_ca)
                            
                            profit = final_amount - Decimal('1')
                            profit_percentage = float(profit) * 100
                            
                            if profit_percentage > self.min_profit_threshold * 100:
                                opportunity = ArbitrageOpportunity(
                                    arbitrage_type=ArbitrageType.TRIANGULAR,
                                    exchanges=[exchange_name],
                                    currency_path=[curr_a, curr_b, curr_c, curr_a],
                                    symbol_path=[
                                        graph.edges[edge_ab]['symbol'],
                                        graph.edges[edge_bc]['symbol'],
                                        graph.edges[edge_ca]['symbol']
                                    ],
                                    price_path=[price_ab, price_bc, price_ca],
                                    amounts=[Decimal('100')],  # To be calculated based on balance
                                    expected_profit=profit * Decimal('100'),  # For $100 investment
                                    profit_percentage=profit_percentage,
                                    execution_time_estimate=2.0,  # Estimated 2 seconds
                                    risk_score=0.3,
                                    confidence=0.9,
                                    min_capital_required=self.min_capital_requirement
                                )
                                
                                opportunities.append(opportunity)
                                
                                logger.info(f"🔍 [TRIANGULAR] Found opportunity: {curr_a}->{curr_b}->{curr_c}->{curr_a}")
                                logger.info(f"🔍 [TRIANGULAR] Profit: {profit_percentage:.4f}%")
                
                except Exception as e:
                    logger.warning(f"⚠️ [TRIANGULAR] Error scanning {exchange_name}: {e}")
                    continue
            
            logger.info(f"🔍 [TRIANGULAR] Found {len(opportunities)} triangular arbitrage opportunities")
            
        except Exception as e:
            logger.error(f"❌ [TRIANGULAR] Error finding triangular arbitrage: {e}")
        
        return opportunities
    
    async def find_cross_exchange_arbitrage_opportunities(self) -> List[ArbitrageOpportunity]:
        """Find arbitrage opportunities across different exchanges"""
        opportunities = []
        
        try:
            logger.info("🔍 [CROSS-EXCHANGE] Scanning for cross-exchange arbitrage opportunities...")
            
            # Find common trading pairs across exchanges
            common_pairs = {}
            
            for exchange_name, graph in self.currency_graphs.items():
                for (base, quote), edge_data in graph.edges.items():
                    symbol = edge_data['symbol']
                    if symbol not in common_pairs:
                        common_pairs[symbol] = {}
                    common_pairs[symbol][exchange_name] = edge_data
            
            # Look for price discrepancies
            for symbol, exchange_data in common_pairs.items():
                if len(exchange_data) >= 2:  # Available on at least 2 exchanges
                    prices = {exchange: data['price'] for exchange, data in exchange_data.items()}
                    
                    min_price_exchange = min(prices, key=prices.get)
                    max_price_exchange = max(prices, key=prices.get)
                    
                    min_price = prices[min_price_exchange]
                    max_price = prices[max_price_exchange]
                    
                    # Calculate profit percentage
                    profit_percentage = float((max_price - min_price) / min_price) * 100
                    
                    if profit_percentage > self.min_profit_threshold * 100:
                        # Account for trading fees (assume 0.1% per trade)
                        net_profit_percentage = profit_percentage - 0.2  # 0.1% * 2 trades
                        
                        if net_profit_percentage > 0:
                            opportunity = ArbitrageOpportunity(
                                arbitrage_type=ArbitrageType.CROSS_EXCHANGE,
                                exchanges=[min_price_exchange, max_price_exchange],
                                currency_path=[symbol.replace('USDT', '').replace('USD', ''), 'USDT'],
                                symbol_path=[symbol],
                                price_path=[min_price, max_price],
                                amounts=[Decimal('100')],  # To be calculated
                                expected_profit=Decimal(str(net_profit_percentage)),
                                profit_percentage=net_profit_percentage,
                                execution_time_estimate=3.0,  # Cross-exchange takes longer
                                risk_score=0.4,  # Higher risk due to cross-exchange
                                confidence=0.8,
                                min_capital_required=self.min_capital_requirement
                            )
                            
                            opportunities.append(opportunity)
                            
                            logger.info(f"🔍 [CROSS-EXCHANGE] Found opportunity: {symbol}")
                            logger.info(f"🔍 [CROSS-EXCHANGE] Buy on {min_price_exchange} @ {min_price}")
                            logger.info(f"🔍 [CROSS-EXCHANGE] Sell on {max_price_exchange} @ {max_price}")
                            logger.info(f"🔍 [CROSS-EXCHANGE] Net profit: {net_profit_percentage:.4f}%")
            
            logger.info(f"🔍 [CROSS-EXCHANGE] Found {len(opportunities)} cross-exchange arbitrage opportunities")
            
        except Exception as e:
            logger.error(f"❌ [CROSS-EXCHANGE] Error finding cross-exchange arbitrage: {e}")
        
        return opportunities
    
    async def find_cross_currency_arbitrage_opportunities(self) -> List[ArbitrageOpportunity]:
        """Find arbitrage opportunities between correlated currency pairs"""
        opportunities = []
        
        try:
            logger.info("🔍 [CROSS-CURRENCY] Scanning for cross-currency arbitrage opportunities...")
            
            # Define correlated currency pairs
            correlated_pairs = [
                (['BTC', 'ETH'], 'USDT'),  # BTC/USDT vs ETH/USDT correlation
                (['ETH', 'SOL'], 'USDT'),  # ETH/USDT vs SOL/USDT correlation
                (['BTC', 'SOL'], 'USDT'),  # BTC/USDT vs SOL/USDT correlation
                (['ADA', 'DOT'], 'USDT'),  # ADA/USDT vs DOT/USDT correlation
            ]
            
            for exchange_name, graph in self.currency_graphs.items():
                for currencies, quote in correlated_pairs:
                    if len(currencies) >= 2:
                        curr_a, curr_b = currencies[0], currencies[1]
                        
                        # Check if both USDT pairs exist (skip direct crypto-to-crypto pairs)
                        edge_a = (curr_a, quote)
                        edge_b = (curr_b, quote)
                        # ENHANCED: Implement USDT-based cross-currency arbitrage
                        # Instead of direct crypto-to-crypto pairs, use USDT as intermediary
                        # Example: BTC -> USDT -> SOL (if profitable)
                        try:
                            # Check if both currencies have USDT pairs
                            btc_usdt_pair = f"{curr_a}USDT"
                            sol_usdt_pair = f"{curr_b}USDT"

                            if (btc_usdt_pair in graph.edges and sol_usdt_pair in graph.edges):
                                # Calculate cross-currency arbitrage via USDT
                                btc_price = graph.edges[btc_usdt_pair].get('price', 0)
                                sol_price = graph.edges[sol_usdt_pair].get('price', 0)

                                if btc_price > 0 and sol_price > 0:
                                    # Calculate implied cross rate
                                    implied_rate = btc_price / sol_price

                                    # Check for arbitrage opportunity (minimum 0.5% profit)
                                    min_profit = 0.005
                                    if implied_rate > (1 + min_profit):
                                        opportunity = ArbitrageOpportunity(
                                            arbitrage_type=ArbitrageType.CROSS_CURRENCY,
                                            symbol_path=[btc_usdt_pair, sol_usdt_pair],
                                            price_path=[btc_price, sol_price],
                                            exchanges=[exchange_name, exchange_name],
                                            profit_percentage=(implied_rate - 1) * 100,
                                            confidence=0.75,
                                            execution_time_estimate=3.0,
                                            required_balance={curr_a: 0.001, 'USDT': 10.0}
                                        )
                                        opportunities.append(opportunity)
                                        logger.info(f"🔍 [CROSS-CURRENCY] Found {curr_a}->{curr_b} via USDT: {implied_rate:.4f} ({(implied_rate-1)*100:.2f}% profit)")
                        except Exception as cross_error:
                            logger.debug(f"[CROSS-CURRENCY] Error calculating {curr_a}-{curr_b}: {cross_error}")
                            continue
            
            logger.info(f"🔍 [CROSS-CURRENCY] Found {len(opportunities)} cross-currency arbitrage opportunities")
            
        except Exception as e:
            logger.error(f"❌ [CROSS-CURRENCY] Error finding cross-currency arbitrage: {e}")
        
        return opportunities
    
    async def find_all_arbitrage_opportunities(self) -> List[ArbitrageOpportunity]:
        """Find all types of arbitrage opportunities"""
        try:
            logger.info("🔍 [ARBITRAGE] Scanning for all arbitrage opportunities...")
            
            all_opportunities = []
            
            # Find triangular arbitrage
            triangular_opportunities = await self.find_triangular_arbitrage_opportunities()
            all_opportunities.extend(triangular_opportunities)
            
            # Find cross-exchange arbitrage
            cross_exchange_opportunities = await self.find_cross_exchange_arbitrage_opportunities()
            all_opportunities.extend(cross_exchange_opportunities)
            
            # Find cross-currency arbitrage
            cross_currency_opportunities = await self.find_cross_currency_arbitrage_opportunities()
            all_opportunities.extend(cross_currency_opportunities)
            
            # Sort by profit percentage (descending)
            all_opportunities.sort(key=lambda x: x.profit_percentage, reverse=True)
            
            # Update statistics
            self.arbitrage_stats['opportunities_found'] = len(all_opportunities)
            
            logger.info(f"🔍 [ARBITRAGE] Total opportunities found: {len(all_opportunities)}")
            
            return all_opportunities
            
        except Exception as e:
            logger.error(f"❌ [ARBITRAGE] Error finding arbitrage opportunities: {e}")
            return []
    
    async def initialize_cross_exchange_monitoring(self):
        """Initialize cross-exchange price monitoring"""
        logger.info("📡 [ARBITRAGE] Initializing cross-exchange monitoring...")
        # Implementation for real-time price monitoring
        pass
    
    async def calculate_currency_correlations(self):
        """Calculate currency correlations for risk management"""
        logger.info("📊 [ARBITRAGE] Calculating currency correlations...")
        # Implementation for correlation analysis
        pass

    async def execute_arbitrage_opportunity(self, opportunity: ArbitrageOpportunity) -> Dict[str, Any]:
        """Execute an arbitrage opportunity with proper risk management"""
        try:
            logger.info(f"⚡ [ARBITRAGE-EXEC] Executing {opportunity.arbitrage_type.value} arbitrage")
            logger.info(f"⚡ [ARBITRAGE-EXEC] Expected profit: {opportunity.profit_percentage:.4f}%")

            # Pre-execution validation
            validation_result = await self._validate_arbitrage_execution(opportunity)
            if not validation_result['valid']:
                return {"success": False, "error": validation_result['reason']}

            # Execute based on arbitrage type
            if opportunity.arbitrage_type == ArbitrageType.TRIANGULAR:
                result = await self._execute_triangular_arbitrage(opportunity)
            elif opportunity.arbitrage_type == ArbitrageType.CROSS_EXCHANGE:
                result = await self._execute_cross_exchange_arbitrage(opportunity)
            elif opportunity.arbitrage_type == ArbitrageType.CROSS_CURRENCY:
                result = await self._execute_cross_currency_arbitrage(opportunity)
            else:
                result = {"success": False, "error": f"Unsupported arbitrage type: {opportunity.arbitrage_type}"}

            # Update statistics
            self._update_arbitrage_stats(opportunity, result)

            return result

        except Exception as e:
            logger.error(f"❌ [ARBITRAGE-EXEC] Error executing arbitrage: {e}")
            return {"success": False, "error": str(e)}

    async def _validate_arbitrage_execution(self, opportunity: ArbitrageOpportunity) -> Dict[str, Any]:
        """Validate that an arbitrage opportunity can be executed"""
        try:
            # Check if all required exchanges are available
            for exchange_name in opportunity.exchanges:
                if exchange_name not in self.exchange_clients:
                    return {"valid": False, "reason": f"Exchange {exchange_name} not available"}

            # Check minimum capital requirement
            if opportunity.min_capital_required > self.max_position_size:
                return {"valid": False, "reason": "Capital requirement exceeds maximum position size"}

            # Check execution time estimate
            if opportunity.execution_time_estimate > self.max_execution_time:
                return {"valid": False, "reason": "Execution time estimate too high"}

            # Check profit threshold
            if opportunity.profit_percentage < self.min_profit_threshold * 100:
                return {"valid": False, "reason": "Profit below minimum threshold"}

            return {"valid": True, "reason": "Validation passed"}

        except Exception as e:
            return {"valid": False, "reason": f"Validation error: {str(e)}"}

    async def _execute_triangular_arbitrage(self, opportunity: ArbitrageOpportunity) -> Dict[str, Any]:
        """Execute triangular arbitrage within a single exchange"""
        try:
            exchange_name = opportunity.exchanges[0]
            client = self.exchange_clients[exchange_name]

            logger.info(f"🔄 [TRIANGULAR] Executing on {exchange_name}")
            logger.info(f"🔄 [TRIANGULAR] Path: {' -> '.join(opportunity.currency_path)}")

            # For safety, implement as a simulation first
            # In production, this would execute the three trades in sequence

            executed_trades = []
            total_profit = Decimal('0')

            # Simulate the three-step arbitrage
            for i, symbol in enumerate(opportunity.symbol_path):
                try:
                    # In real implementation, place actual orders here
                    logger.info(f"🔄 [TRIANGULAR] Step {i+1}: Trading {symbol}")

                    # Simulate trade execution
                    simulated_result = {
                        "order_id": f"sim_{int(time.time())}_{i}",
                        "symbol": symbol,
                        "status": "filled",
                        "exchange": exchange_name
                    }

                    executed_trades.append(simulated_result)

                except Exception as e:
                    logger.error(f"❌ [TRIANGULAR] Error in step {i+1}: {e}")
                    return {"success": False, "error": f"Step {i+1} failed: {str(e)}"}

            logger.info(f"✅ [TRIANGULAR] Successfully executed {len(executed_trades)} trades")

            return {
                "success": True,
                "arbitrage_type": "triangular",
                "executed_trades": executed_trades,
                "expected_profit": float(opportunity.expected_profit),
                "execution_time": opportunity.execution_time_estimate
            }

        except Exception as e:
            logger.error(f"❌ [TRIANGULAR] Execution error: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_cross_exchange_arbitrage(self, opportunity: ArbitrageOpportunity) -> Dict[str, Any]:
        """Execute cross-exchange arbitrage"""
        try:
            buy_exchange = opportunity.exchanges[0]
            sell_exchange = opportunity.exchanges[1]

            logger.info(f"🔄 [CROSS-EXCHANGE] Buy on {buy_exchange}, Sell on {sell_exchange}")

            # For safety, implement as simulation
            # In production, this would execute simultaneous buy/sell orders

            symbol = opportunity.symbol_path[0]
            buy_price = opportunity.price_path[0]
            sell_price = opportunity.price_path[1]

            logger.info(f"🔄 [CROSS-EXCHANGE] {symbol}: Buy @ {buy_price}, Sell @ {sell_price}")

            # Simulate execution
            executed_trades = [
                {
                    "order_id": f"sim_buy_{int(time.time())}",
                    "symbol": symbol,
                    "side": "buy",
                    "price": float(buy_price),
                    "exchange": buy_exchange,
                    "status": "filled"
                },
                {
                    "order_id": f"sim_sell_{int(time.time())}",
                    "symbol": symbol,
                    "side": "sell",
                    "price": float(sell_price),
                    "exchange": sell_exchange,
                    "status": "filled"
                }
            ]

            logger.info(f"✅ [CROSS-EXCHANGE] Successfully executed cross-exchange arbitrage")

            return {
                "success": True,
                "arbitrage_type": "cross_exchange",
                "executed_trades": executed_trades,
                "expected_profit": float(opportunity.expected_profit),
                "execution_time": opportunity.execution_time_estimate
            }

        except Exception as e:
            logger.error(f"❌ [CROSS-EXCHANGE] Execution error: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_cross_currency_arbitrage(self, opportunity: ArbitrageOpportunity) -> Dict[str, Any]:
        """Execute cross-currency arbitrage"""
        try:
            exchange_name = opportunity.exchanges[0]

            logger.info(f"🔄 [CROSS-CURRENCY] Executing on {exchange_name}")
            logger.info(f"🔄 [CROSS-CURRENCY] Currencies: {' -> '.join(opportunity.currency_path)}")

            # For safety, implement as simulation
            # In production, this would execute the currency conversion trades

            executed_trades = []

            for i, symbol in enumerate(opportunity.symbol_path):
                simulated_result = {
                    "order_id": f"sim_cross_{int(time.time())}_{i}",
                    "symbol": symbol,
                    "price": float(opportunity.price_path[i]) if i < len(opportunity.price_path) else 0,
                    "exchange": exchange_name,
                    "status": "filled"
                }
                executed_trades.append(simulated_result)

            logger.info(f"✅ [CROSS-CURRENCY] Successfully executed cross-currency arbitrage")

            return {
                "success": True,
                "arbitrage_type": "cross_currency",
                "executed_trades": executed_trades,
                "expected_profit": float(opportunity.expected_profit),
                "execution_time": opportunity.execution_time_estimate
            }

        except Exception as e:
            logger.error(f"❌ [CROSS-CURRENCY] Execution error: {e}")
            return {"success": False, "error": str(e)}

    def _update_arbitrage_stats(self, opportunity: ArbitrageOpportunity, result: Dict[str, Any]):
        """Update arbitrage execution statistics"""
        try:
            self.arbitrage_stats['opportunities_executed'] += 1

            if result.get('success', False):
                self.arbitrage_stats['total_profit'] += opportunity.expected_profit

            self.arbitrage_stats['success_rate'] = (
                self.arbitrage_stats['opportunities_executed'] /
                max(self.arbitrage_stats['opportunities_found'], 1)
            )

            logger.info(f"📊 [ARBITRAGE-STATS] Success rate: {self.arbitrage_stats['success_rate']:.2%}")
            logger.info(f"📊 [ARBITRAGE-STATS] Total profit: ${self.arbitrage_stats['total_profit']:.2f}")

        except Exception as e:
            logger.error(f"❌ [ARBITRAGE-STATS] Error updating stats: {e}")

    async def run_continuous_arbitrage_scanning(self):
        """Run continuous arbitrage opportunity scanning"""
        try:
            logger.info("🚀 [ARBITRAGE] Starting continuous arbitrage scanning...")

            while True:
                try:
                    # Refresh currency graphs
                    await self.build_currency_graphs()

                    # Find arbitrage opportunities
                    opportunities = await self.find_all_arbitrage_opportunities()

                    if opportunities:
                        # Execute the best opportunity
                        best_opportunity = opportunities[0]

                        logger.info(f"🎯 [ARBITRAGE] Best opportunity: {best_opportunity.arbitrage_type.value}")
                        logger.info(f"🎯 [ARBITRAGE] Profit: {best_opportunity.profit_percentage:.4f}%")

                        # Execute the opportunity (currently simulated)
                        result = await self.execute_arbitrage_opportunity(best_opportunity)

                        if result.get('success', False):
                            logger.info("✅ [ARBITRAGE] Successfully executed arbitrage opportunity")
                        else:
                            logger.warning(f"⚠️ [ARBITRAGE] Failed to execute: {result.get('error')}")
                    else:
                        logger.info("🔍 [ARBITRAGE] No arbitrage opportunities found")

                    # Wait before next scan
                    await asyncio.sleep(30)  # 30 second intervals for arbitrage

                except Exception as e:
                    logger.error(f"❌ [ARBITRAGE] Error in scanning loop: {e}")
                    await asyncio.sleep(60)  # Wait longer on error

        except Exception as e:
            logger.error(f"❌ [ARBITRAGE] Critical error in continuous scanning: {e}")
            raise
