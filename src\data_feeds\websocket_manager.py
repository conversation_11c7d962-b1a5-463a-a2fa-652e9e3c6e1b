"""
WebSocket Management System for Real-time Data Feeds
Provides robust WebSocket connections with automatic reconnection, routing, and monitoring
"""

import asyncio
import websockets
import json
import time
import logging
from typing import Dict, List, Optional, Callable, Any, Set
from dataclasses import dataclass, field
from datetime import datetime
import threading
from enum import Enum

logger = logging.getLogger(__name__)

class ConnectionState(Enum):
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"

@dataclass
class WebSocketConfig:
    """WebSocket connection configuration"""
    url: str
    max_reconnect_attempts: int = 10
    reconnect_delay: float = 5.0
    ping_interval: float = 30.0
    ping_timeout: float = 10.0
    max_message_size: int = 1024 * 1024  # 1MB
    compression: Optional[str] = None

@dataclass
class SubscriptionInfo:
    """WebSocket subscription information"""
    channel: str
    symbol: str
    callback: Callable
    subscription_message: Dict[str, Any]
    active: bool = True

class WebSocketManager:
    """
    Enterprise-grade WebSocket manager with automatic reconnection and subscription management
    """
    
    def __init__(self, config: WebSocketConfig):
        self.config = config
        self.websocket = None
        self.state = ConnectionState.DISCONNECTED
        self.subscriptions: Dict[str, SubscriptionInfo] = {}
        self.message_handlers: List[Callable] = []
        self.reconnect_attempts = 0
        self.last_ping = None
        self.last_pong = None
        self.running = False
        self.logger = logging.getLogger(__name__)
        
        # Statistics
        self.stats = {
            'messages_received': 0,
            'messages_sent': 0,
            'reconnections': 0,
            'connection_time': None,
            'last_message_time': None
        }
        
    async def connect(self) -> bool:
        """Establish WebSocket connection"""
        if self.state == ConnectionState.CONNECTED:
            return True
            
        self.state = ConnectionState.CONNECTING
        self.logger.info(f"Connecting to {self.config.url}")
        
        try:
            self.websocket = await websockets.connect(
                self.config.url,
                ping_interval=self.config.ping_interval,
                ping_timeout=self.config.ping_timeout,
                max_size=self.config.max_message_size,
                compression=self.config.compression
            )
            
            self.state = ConnectionState.CONNECTED
            self.stats['connection_time'] = datetime.now()
            self.reconnect_attempts = 0
            self.logger.info("WebSocket connected successfully")
            
            # Resubscribe to all active subscriptions
            await self._resubscribe_all()
            
            return True
            
        except Exception as e:
            self.state = ConnectionState.ERROR
            self.logger.error(f"Failed to connect: {e}")
            return False
            
    async def disconnect(self) -> None:
        """Disconnect WebSocket"""
        self.running = False
        
        if self.websocket:
            await self.websocket.close()
            self.websocket = None
            
        self.state = ConnectionState.DISCONNECTED
        self.logger.info("WebSocket disconnected")
        
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """Send message through WebSocket"""
        if self.state != ConnectionState.CONNECTED or not self.websocket:
            self.logger.warning("Cannot send message: not connected")
            return False
            
        try:
            await self.websocket.send(json.dumps(message))
            self.stats['messages_sent'] += 1
            return True
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}")
            return False
            
    async def subscribe(self, channel: str, symbol: str, callback: Callable, 
                       subscription_message: Optional[Dict[str, Any]] = None) -> str:
        """Subscribe to a channel/symbol"""
        subscription_id = f"{channel}:{symbol}"
        
        if not subscription_message:
            subscription_message = {
                "op": "subscribe",
                "args": [f"{channel}.{symbol}"]
            }
            
        subscription = SubscriptionInfo(
            channel=channel,
            symbol=symbol,
            callback=callback,
            subscription_message=subscription_message
        )
        
        self.subscriptions[subscription_id] = subscription
        
        # Send subscription if connected
        if self.state == ConnectionState.CONNECTED:
            success = await self.send_message(subscription_message)
            if success:
                self.logger.info(f"Subscribed to {subscription_id}")
            else:
                self.logger.error(f"Failed to subscribe to {subscription_id}")
                
        return subscription_id
        
    async def unsubscribe(self, subscription_id: str) -> bool:
        """Unsubscribe from a channel/symbol"""
        if subscription_id not in self.subscriptions:
            return False
            
        subscription = self.subscriptions[subscription_id]
        subscription.active = False
        
        # Send unsubscribe message
        unsubscribe_message = {
            "op": "unsubscribe",
            "args": [f"{subscription.channel}.{subscription.symbol}"]
        }
        
        success = await self.send_message(unsubscribe_message)
        if success:
            del self.subscriptions[subscription_id]
            self.logger.info(f"Unsubscribed from {subscription_id}")
            
        return success
        
    def add_message_handler(self, handler: Callable) -> None:
        """Add a global message handler"""
        self.message_handlers.append(handler)
        
    async def start_listening(self) -> None:
        """Start listening for messages"""
        self.running = True
        
        while self.running:
            try:
                if self.state != ConnectionState.CONNECTED:
                    await self._attempt_reconnect()
                    continue
                    
                # Listen for messages
                try:
                    message = await asyncio.wait_for(
                        self.websocket.recv(), 
                        timeout=self.config.ping_timeout
                    )
                    
                    await self._handle_message(message)
                    
                except asyncio.TimeoutError:
                    # Check connection health
                    if not await self._check_connection_health():
                        self.state = ConnectionState.ERROR
                        
                except websockets.exceptions.ConnectionClosed:
                    self.logger.warning("WebSocket connection closed")
                    self.state = ConnectionState.ERROR
                    
            except Exception as e:
                self.logger.error(f"Error in message listening: {e}")
                self.state = ConnectionState.ERROR
                await asyncio.sleep(1)
                
    async def _handle_message(self, raw_message: str) -> None:
        """Handle incoming WebSocket message"""
        try:
            message = json.loads(raw_message)
            self.stats['messages_received'] += 1
            self.stats['last_message_time'] = datetime.now()
            
            # Route message to appropriate subscription
            await self._route_message(message)
            
            # Call global message handlers
            for handler in self.message_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(message)
                    else:
                        handler(message)
                except Exception as e:
                    self.logger.error(f"Error in message handler: {e}")
                    
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse message: {e}")
        except Exception as e:
            self.logger.error(f"Error handling message: {e}")
            
    async def _route_message(self, message: Dict[str, Any]) -> None:
        """Route message to appropriate subscription callback"""
        # Extract topic/channel information
        topic = message.get('topic', '')
        
        # Find matching subscription
        for subscription_id, subscription in self.subscriptions.items():
            if subscription.active and self._message_matches_subscription(message, subscription):
                try:
                    if asyncio.iscoroutinefunction(subscription.callback):
                        await subscription.callback(message)
                    else:
                        subscription.callback(message)
                except Exception as e:
                    self.logger.error(f"Error in subscription callback {subscription_id}: {e}")
                    
    def _message_matches_subscription(self, message: Dict[str, Any], subscription: SubscriptionInfo) -> bool:
        """Check if message matches subscription"""
        topic = message.get('topic', '')
        return f"{subscription.channel}.{subscription.symbol}" in topic
        
    async def _attempt_reconnect(self) -> None:
        """Attempt to reconnect WebSocket"""
        if self.reconnect_attempts >= self.config.max_reconnect_attempts:
            self.logger.error("Max reconnection attempts reached")
            self.running = False
            return
            
        self.state = ConnectionState.RECONNECTING
        self.reconnect_attempts += 1
        
        self.logger.info(f"Reconnection attempt {self.reconnect_attempts}/{self.config.max_reconnect_attempts}")
        
        await asyncio.sleep(self.config.reconnect_delay)
        
        if await self.connect():
            self.stats['reconnections'] += 1
        else:
            # Exponential backoff
            self.config.reconnect_delay = min(self.config.reconnect_delay * 2, 60)
            
    async def _resubscribe_all(self) -> None:
        """Resubscribe to all active subscriptions"""
        for subscription_id, subscription in self.subscriptions.items():
            if subscription.active:
                await self.send_message(subscription.subscription_message)
                self.logger.info(f"Resubscribed to {subscription_id}")
                
    async def _check_connection_health(self) -> bool:
        """Check WebSocket connection health"""
        if not self.websocket:
            return False
            
        try:
            pong_waiter = await self.websocket.ping()
            await asyncio.wait_for(pong_waiter, timeout=self.config.ping_timeout)
            return True
        except Exception:
            return False
            
    def get_stats(self) -> Dict[str, Any]:
        """Get WebSocket statistics"""
        return {
            **self.stats,
            'state': self.state.value,
            'active_subscriptions': len([s for s in self.subscriptions.values() if s.active]),
            'reconnect_attempts': self.reconnect_attempts
        }

class StreamingDataRouter:
    """
    Routes streaming data to multiple consumers with filtering and transformation
    """
    
    def __init__(self):
        self.routes: Dict[str, List[Callable]] = {}
        self.filters: Dict[str, Callable] = {}
        self.transformers: Dict[str, Callable] = {}
        self.logger = logging.getLogger(__name__)
        
    def add_route(self, pattern: str, handler: Callable, 
                  filter_func: Optional[Callable] = None,
                  transformer: Optional[Callable] = None) -> None:
        """Add a data route"""
        if pattern not in self.routes:
            self.routes[pattern] = []
            
        self.routes[pattern].append(handler)
        
        if filter_func:
            self.filters[f"{pattern}:{len(self.routes[pattern])-1}"] = filter_func
            
        if transformer:
            self.transformers[f"{pattern}:{len(self.routes[pattern])-1}"] = transformer
            
        self.logger.info(f"Added route for pattern: {pattern}")
        
    async def route_data(self, data: Dict[str, Any]) -> None:
        """Route data to matching handlers"""
        for pattern, handlers in self.routes.items():
            if self._matches_pattern(data, pattern):
                for i, handler in enumerate(handlers):
                    try:
                        # Apply filter if exists
                        filter_key = f"{pattern}:{i}"
                        if filter_key in self.filters:
                            if not self.filters[filter_key](data):
                                continue
                                
                        # Apply transformer if exists
                        processed_data = data
                        if filter_key in self.transformers:
                            processed_data = self.transformers[filter_key](data)
                            
                        # Call handler
                        if asyncio.iscoroutinefunction(handler):
                            await handler(processed_data)
                        else:
                            handler(processed_data)
                            
                    except Exception as e:
                        self.logger.error(f"Error in route handler: {e}")
                        
    def _matches_pattern(self, data: Dict[str, Any], pattern: str) -> bool:
        """Check if data matches routing pattern"""
        # Simple pattern matching - can be enhanced
        topic = data.get('topic', '')
        return pattern in topic or pattern == '*'

class DataFeedsDashboard:
    """
    Dashboard for monitoring data feeds and WebSocket connections
    """
    
    def __init__(self):
        self.websocket_managers: Dict[str, WebSocketManager] = {}
        self.data_routers: Dict[str, StreamingDataRouter] = {}
        
    def register_websocket_manager(self, name: str, manager: WebSocketManager) -> None:
        """Register a WebSocket manager"""
        self.websocket_managers[name] = manager
        
    def register_data_router(self, name: str, router: StreamingDataRouter) -> None:
        """Register a data router"""
        self.data_routers[name] = router
        
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive dashboard data"""
        return {
            'websocket_managers': {
                name: manager.get_stats() 
                for name, manager in self.websocket_managers.items()
            },
            'data_routers': {
                name: {
                    'routes_count': len(router.routes),
                    'filters_count': len(router.filters),
                    'transformers_count': len(router.transformers)
                }
                for name, router in self.data_routers.items()
            },
            'timestamp': datetime.now().isoformat()
        }

# Global instances
streaming_data_router = StreamingDataRouter()
data_feeds_dashboard = DataFeedsDashboard()
