"""
Data Validation System for Market Data
Provides comprehensive validation, sanitization, and quality checks for trading data
"""

import re
import math
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Result of data validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    cleaned_data: Optional[Any] = None

@dataclass
class ValidationRule:
    """Data validation rule"""
    name: str
    field: str
    rule_type: str  # 'range', 'format', 'required', 'custom'
    parameters: Dict[str, Any]
    severity: str = 'error'  # 'error', 'warning'

class DataValidator:
    """
    Enterprise-grade data validator for market data with real-time validation
    """
    
    def __init__(self):
        self.rules = {}
        self.validation_stats = {
            'total_validations': 0,
            'passed_validations': 0,
            'failed_validations': 0,
            'warnings_generated': 0
        }
        self.logger = logging.getLogger(__name__)
        
        # Setup default validation rules
        self._setup_default_rules()
        
    def add_rule(self, rule: ValidationRule) -> None:
        """Add a validation rule"""
        if rule.field not in self.rules:
            self.rules[rule.field] = []
        self.rules[rule.field].append(rule)
        self.logger.info(f"Added validation rule: {rule.name} for field {rule.field}")
        
    def validate_price_data(self, data: Dict[str, Any]) -> ValidationResult:
        """Validate price data structure and values"""
        errors = []
        warnings = []
        cleaned_data = data.copy()
        
        # Required fields check
        required_fields = ['symbol', 'price', 'timestamp']
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
                
        # Symbol validation
        if 'symbol' in data:
            symbol_result = self._validate_symbol(data['symbol'])
            if not symbol_result.is_valid:
                errors.extend(symbol_result.errors)
            else:
                cleaned_data['symbol'] = symbol_result.cleaned_data
                
        # Price validation
        if 'price' in data:
            price_result = self._validate_price(data['price'])
            if not price_result.is_valid:
                errors.extend(price_result.errors)
            warnings.extend(price_result.warnings)
            if price_result.cleaned_data is not None:
                cleaned_data['price'] = price_result.cleaned_data
                
        # Volume validation
        if 'volume' in data:
            volume_result = self._validate_volume(data['volume'])
            if not volume_result.is_valid:
                errors.extend(volume_result.errors)
            warnings.extend(volume_result.warnings)
            if volume_result.cleaned_data is not None:
                cleaned_data['volume'] = volume_result.cleaned_data
                
        # Timestamp validation
        if 'timestamp' in data:
            timestamp_result = self._validate_timestamp(data['timestamp'])
            if not timestamp_result.is_valid:
                errors.extend(timestamp_result.errors)
            warnings.extend(timestamp_result.warnings)
            if timestamp_result.cleaned_data is not None:
                cleaned_data['timestamp'] = timestamp_result.cleaned_data
                
        # Cross-field validation
        cross_validation_result = self._validate_cross_fields(cleaned_data)
        errors.extend(cross_validation_result.errors)
        warnings.extend(cross_validation_result.warnings)
        
        # Update statistics
        self.validation_stats['total_validations'] += 1
        if errors:
            self.validation_stats['failed_validations'] += 1
        else:
            self.validation_stats['passed_validations'] += 1
        self.validation_stats['warnings_generated'] += len(warnings)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            cleaned_data=cleaned_data if len(errors) == 0 else None
        )
        
    def validate_order_data(self, data: Dict[str, Any]) -> ValidationResult:
        """Validate trading order data"""
        errors = []
        warnings = []
        cleaned_data = data.copy()
        
        # Required fields for orders
        required_fields = ['symbol', 'side', 'quantity', 'price']
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required order field: {field}")
                
        # Side validation
        if 'side' in data:
            if data['side'].upper() not in ['BUY', 'SELL']:
                errors.append(f"Invalid order side: {data['side']}")
            else:
                cleaned_data['side'] = data['side'].upper()
                
        # Quantity validation
        if 'quantity' in data:
            try:
                quantity = float(data['quantity'])
                if quantity <= 0:
                    errors.append("Order quantity must be positive")
                elif quantity < 0.00001:  # Minimum quantity check
                    warnings.append("Very small order quantity detected")
                cleaned_data['quantity'] = quantity
            except (ValueError, TypeError):
                errors.append(f"Invalid quantity format: {data['quantity']}")
                
        # Order type validation
        if 'order_type' in data:
            valid_types = ['MARKET', 'LIMIT', 'STOP', 'STOP_LIMIT']
            if data['order_type'].upper() not in valid_types:
                errors.append(f"Invalid order type: {data['order_type']}")
            else:
                cleaned_data['order_type'] = data['order_type'].upper()
                
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            cleaned_data=cleaned_data if len(errors) == 0 else None
        )
        
    def _validate_symbol(self, symbol: Any) -> ValidationResult:
        """Validate trading symbol"""
        errors = []
        warnings = []
        
        if not isinstance(symbol, str):
            errors.append(f"Symbol must be string, got {type(symbol)}")
            return ValidationResult(False, errors, warnings)
            
        # Clean symbol
        cleaned_symbol = symbol.upper().strip()
        
        # Symbol format validation
        if not re.match(r'^[A-Z0-9]{2,20}$', cleaned_symbol):
            errors.append(f"Invalid symbol format: {symbol}")
            
        # Common symbol patterns
        if not (cleaned_symbol.endswith('USDT') or 
                cleaned_symbol.endswith('USD') or 
                cleaned_symbol.endswith('BTC') or 
                cleaned_symbol.endswith('ETH')):
            warnings.append(f"Unusual symbol format: {cleaned_symbol}")
            
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            cleaned_data=cleaned_symbol
        )
        
    def _validate_price(self, price: Any) -> ValidationResult:
        """Validate price value"""
        errors = []
        warnings = []
        
        try:
            price_float = float(price)
        except (ValueError, TypeError):
            errors.append(f"Invalid price format: {price}")
            return ValidationResult(False, errors, warnings)
            
        # Price range validation
        if price_float <= 0:
            errors.append("Price must be positive")
        elif price_float > 1000000:  # Sanity check
            warnings.append(f"Very high price detected: {price_float}")
        elif price_float < 0.00000001:  # Very small price
            warnings.append(f"Very small price detected: {price_float}")
            
        # Check for NaN or infinity
        if math.isnan(price_float) or math.isinf(price_float):
            errors.append("Price cannot be NaN or infinity")
            
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            cleaned_data=price_float
        )
        
    def _validate_volume(self, volume: Any) -> ValidationResult:
        """Validate volume value"""
        errors = []
        warnings = []
        
        try:
            volume_float = float(volume)
        except (ValueError, TypeError):
            errors.append(f"Invalid volume format: {volume}")
            return ValidationResult(False, errors, warnings)
            
        # Volume validation
        if volume_float < 0:
            errors.append("Volume cannot be negative")
        elif volume_float == 0:
            warnings.append("Zero volume detected")
        elif volume_float > 1000000000:  # Sanity check
            warnings.append(f"Very high volume detected: {volume_float}")
            
        # Check for NaN or infinity
        if math.isnan(volume_float) or math.isinf(volume_float):
            errors.append("Volume cannot be NaN or infinity")
            
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            cleaned_data=volume_float
        )
        
    def _validate_timestamp(self, timestamp: Any) -> ValidationResult:
        """Validate timestamp"""
        errors = []
        warnings = []
        cleaned_timestamp = None
        
        # Handle different timestamp formats
        if isinstance(timestamp, datetime):
            cleaned_timestamp = timestamp
        elif isinstance(timestamp, (int, float)):
            try:
                # Assume Unix timestamp
                if timestamp > 1e12:  # Milliseconds
                    cleaned_timestamp = datetime.fromtimestamp(timestamp / 1000)
                else:  # Seconds
                    cleaned_timestamp = datetime.fromtimestamp(timestamp)
            except (ValueError, OSError):
                errors.append(f"Invalid timestamp: {timestamp}")
        elif isinstance(timestamp, str):
            try:
                # Try ISO format
                cleaned_timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            except ValueError:
                errors.append(f"Invalid timestamp format: {timestamp}")
        else:
            errors.append(f"Unsupported timestamp type: {type(timestamp)}")
            
        # Timestamp range validation
        if cleaned_timestamp:
            now = datetime.now()
            if cleaned_timestamp > now + timedelta(minutes=5):
                warnings.append("Timestamp is in the future")
            elif cleaned_timestamp < now - timedelta(days=30):
                warnings.append("Timestamp is very old")
                
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            cleaned_data=cleaned_timestamp
        )
        
    def _validate_cross_fields(self, data: Dict[str, Any]) -> ValidationResult:
        """Validate relationships between fields"""
        errors = []
        warnings = []
        
        # Check bid/ask spread if both present
        if 'bid' in data and 'ask' in data:
            try:
                bid = float(data['bid'])
                ask = float(data['ask'])
                if bid >= ask:
                    errors.append(f"Bid ({bid}) must be less than ask ({ask})")
                elif (ask - bid) / bid > 0.1:  # 10% spread
                    warnings.append(f"Large bid-ask spread: {(ask - bid) / bid * 100:.2f}%")
            except (ValueError, TypeError):
                pass  # Individual field validation will catch this
                
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
        
    def _setup_default_rules(self) -> None:
        """Setup default validation rules"""
        # Price rules
        self.add_rule(ValidationRule(
            name="price_positive",
            field="price",
            rule_type="range",
            parameters={"min": 0, "exclusive_min": True}
        ))
        
        # Volume rules
        self.add_rule(ValidationRule(
            name="volume_non_negative",
            field="volume",
            rule_type="range",
            parameters={"min": 0}
        ))
        
        # Symbol rules
        self.add_rule(ValidationRule(
            name="symbol_format",
            field="symbol",
            rule_type="format",
            parameters={"pattern": r"^[A-Z0-9]{2,20}$"}
        ))
        
    def get_validation_stats(self) -> Dict[str, Any]:
        """Get validation statistics"""
        total = self.validation_stats['total_validations']
        success_rate = self.validation_stats['passed_validations'] / total if total > 0 else 0
        
        return {
            **self.validation_stats,
            'success_rate': success_rate,
            'failure_rate': 1 - success_rate
        }
        
    def reset_stats(self) -> None:
        """Reset validation statistics"""
        self.validation_stats = {
            'total_validations': 0,
            'passed_validations': 0,
            'failed_validations': 0,
            'warnings_generated': 0
        }

# Global validator instance
data_validator = DataValidator()
