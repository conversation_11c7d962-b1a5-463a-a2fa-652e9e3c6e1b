"""
Cache Management System for Market Data
Provides high-performance caching with TTL, LRU eviction, and memory management
"""

import time
import threading
from typing import Any, Dict, Optional, List, Tuple
from collections import OrderedDict
import logging
import pickle
import hashlib
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class MemoryCache:
    """
    High-performance in-memory cache with TTL and LRU eviction
    """
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache = OrderedDict()
        self.timestamps = {}
        self.ttls = {}
        self.lock = threading.RLock()
        self.hits = 0
        self.misses = 0
        
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        with self.lock:
            if key not in self.cache:
                self.misses += 1
                return None
                
            # Check TTL
            if self._is_expired(key):
                self._remove(key)
                self.misses += 1
                return None
                
            # Move to end (LRU)
            value = self.cache.pop(key)
            self.cache[key] = value
            self.hits += 1
            return value
            
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache"""
        with self.lock:
            ttl = ttl or self.default_ttl
            
            # Remove if exists
            if key in self.cache:
                self._remove(key)
                
            # Evict if at capacity
            while len(self.cache) >= self.max_size:
                self._evict_lru()
                
            # Add new entry
            self.cache[key] = value
            self.timestamps[key] = time.time()
            self.ttls[key] = ttl
            
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        with self.lock:
            if key in self.cache:
                self._remove(key)
                return True
            return False
            
    def clear(self) -> None:
        """Clear all cache entries"""
        with self.lock:
            self.cache.clear()
            self.timestamps.clear()
            self.ttls.clear()
            
    def size(self) -> int:
        """Get current cache size"""
        return len(self.cache)
        
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.hits + self.misses
        hit_rate = self.hits / total_requests if total_requests > 0 else 0
        
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': hit_rate,
            'total_requests': total_requests
        }
        
    def _is_expired(self, key: str) -> bool:
        """Check if key is expired"""
        if key not in self.timestamps:
            return True
        return time.time() - self.timestamps[key] > self.ttls[key]
        
    def _remove(self, key: str) -> None:
        """Remove key from all structures"""
        self.cache.pop(key, None)
        self.timestamps.pop(key, None)
        self.ttls.pop(key, None)
        
    def _evict_lru(self) -> None:
        """Evict least recently used item"""
        if self.cache:
            lru_key = next(iter(self.cache))
            self._remove(lru_key)
            
    def cleanup_expired(self) -> int:
        """Remove all expired entries"""
        with self.lock:
            expired_keys = [
                key for key in self.cache.keys()
                if self._is_expired(key)
            ]
            
            for key in expired_keys:
                self._remove(key)
                
            return len(expired_keys)

class CacheManager:
    """
    Advanced cache manager with multiple cache layers and strategies
    """
    
    def __init__(self):
        self.caches = {}
        self.default_cache = MemoryCache()
        self.logger = logging.getLogger(__name__)
        
    def create_cache(self, name: str, max_size: int = 1000, default_ttl: int = 300) -> MemoryCache:
        """Create a named cache"""
        cache = MemoryCache(max_size, default_ttl)
        self.caches[name] = cache
        self.logger.info(f"Created cache '{name}' with max_size={max_size}, ttl={default_ttl}")
        return cache
        
    def get_cache(self, name: str) -> Optional[MemoryCache]:
        """Get named cache"""
        return self.caches.get(name)
        
    def get_or_create_cache(self, name: str, max_size: int = 1000, default_ttl: int = 300) -> MemoryCache:
        """Get existing cache or create new one"""
        if name in self.caches:
            return self.caches[name]
        return self.create_cache(name, max_size, default_ttl)
        
    def cache_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments"""
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()
        
    def cached_call(self, cache_name: str, key: str, func, *args, ttl: Optional[int] = None, **kwargs) -> Any:
        """Execute function with caching"""
        cache = self.get_or_create_cache(cache_name)
        
        # Try to get from cache
        result = cache.get(key)
        if result is not None:
            return result
            
        # Execute function and cache result
        try:
            result = func(*args, **kwargs)
            cache.set(key, result, ttl)
            return result
        except Exception as e:
            self.logger.error(f"Error in cached call: {e}")
            raise
            
    def invalidate_pattern(self, cache_name: str, pattern: str) -> int:
        """Invalidate cache entries matching pattern"""
        cache = self.get_cache(cache_name)
        if not cache:
            return 0
            
        with cache.lock:
            matching_keys = [
                key for key in cache.cache.keys()
                if pattern in key
            ]
            
            for key in matching_keys:
                cache.delete(key)
                
            return len(matching_keys)
            
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all caches"""
        stats = {}
        
        # Default cache stats
        stats['default'] = self.default_cache.get_stats()
        
        # Named cache stats
        for name, cache in self.caches.items():
            stats[name] = cache.get_stats()
            
        return stats
        
    def cleanup_all_expired(self) -> Dict[str, int]:
        """Cleanup expired entries from all caches"""
        results = {}
        
        # Cleanup default cache
        results['default'] = self.default_cache.cleanup_expired()
        
        # Cleanup named caches
        for name, cache in self.caches.items():
            results[name] = cache.cleanup_expired()
            
        return results
        
    def clear_all(self) -> None:
        """Clear all caches"""
        self.default_cache.clear()
        for cache in self.caches.values():
            cache.clear()
        self.logger.info("All caches cleared")

# Global cache manager instance
cache_manager = CacheManager()

def cached(cache_name: str = 'default', ttl: Optional[int] = None, key_func: Optional[callable] = None):
    """
    Decorator for caching function results
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                key = key_func(*args, **kwargs)
            else:
                key = cache_manager.cache_key(func.__name__, *args, **kwargs)
                
            return cache_manager.cached_call(cache_name, key, func, *args, ttl=ttl, **kwargs)
        return wrapper
    return decorator
