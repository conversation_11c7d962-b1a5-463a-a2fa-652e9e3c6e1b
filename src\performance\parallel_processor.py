#!/usr/bin/env python3
"""
PARALLEL PROCESSING OPTIMIZER
Advanced parallel execution system for trading operations with intelligent load balancing
"""

import asyncio
import concurrent.futures
import threading
import time
import logging
import psutil
from typing import Dict, List, Any, Callable, Optional, Tuple, Union
from dataclasses import dataclass
from collections import defaultdict, deque
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class TaskMetrics:
    """Task execution metrics"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    avg_execution_time: float = 0.0
    total_execution_time: float = 0.0
    queue_wait_time: float = 0.0
    
    @property
    def success_rate(self) -> float:
        if self.total_tasks == 0:
            return 0.0
        return (self.completed_tasks / self.total_tasks) * 100
    
    @property
    def throughput(self) -> float:
        if self.total_execution_time == 0:
            return 0.0
        return self.completed_tasks / self.total_execution_time

@dataclass
class WorkerStats:
    """Worker thread statistics"""
    worker_id: str
    tasks_processed: int = 0
    total_time: float = 0.0
    avg_task_time: float = 0.0
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    last_active: float = 0.0

class ParallelProcessor:
    """OPTIMIZED: Advanced parallel processing system for trading operations"""
    
    def __init__(self, max_workers: int = None, enable_gpu: bool = False):
        # Auto-detect optimal worker count
        if max_workers is None:
            cpu_count = psutil.cpu_count(logical=False)
            max_workers = min(cpu_count * 2, 16)  # Cap at 16 for stability
        
        self.max_workers = max_workers
        self.enable_gpu = enable_gpu
        
        # Execution pools
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self.process_pool = concurrent.futures.ProcessPoolExecutor(max_workers=max_workers//2)
        
        # Task queues by priority
        self.high_priority_queue = asyncio.Queue(maxsize=1000)
        self.medium_priority_queue = asyncio.Queue(maxsize=2000)
        self.low_priority_queue = asyncio.Queue(maxsize=5000)
        
        # Performance monitoring
        self.metrics = TaskMetrics()
        self.worker_stats: Dict[str, WorkerStats] = {}
        self.execution_history = deque(maxlen=1000)
        
        # Load balancing
        self.worker_loads: Dict[str, float] = defaultdict(float)
        self.task_routing_stats = defaultdict(int)
        
        # Specialized processors
        self.api_processor = None
        self.neural_processor = None
        self.data_processor = None
        
        # Control flags
        self.running = False
        self.workers_started = False
        
        logger.info(f"🚀 [PARALLEL] Parallel processor initialized with {max_workers} workers")
    
    async def start(self):
        """Start the parallel processing system"""
        if self.running:
            return
        
        self.running = True
        
        # Start specialized processors
        await self._start_specialized_processors()
        
        # Start worker monitoring
        asyncio.create_task(self._monitor_workers())
        
        logger.info("✅ [PARALLEL] Parallel processing system started")
    
    async def stop(self):
        """Stop the parallel processing system"""
        self.running = False
        
        # Shutdown thread pools
        self.thread_pool.shutdown(wait=True)
        self.process_pool.shutdown(wait=True)
        
        logger.info("🛑 [PARALLEL] Parallel processing system stopped")
    
    async def execute_parallel(self, tasks: List[Callable], task_type: str = "general", 
                             priority: int = 2, timeout: float = 30.0) -> List[Any]:
        """Execute multiple tasks in parallel with intelligent routing"""
        if not tasks:
            return []
        
        start_time = time.time()
        
        # Route tasks to appropriate processors
        if task_type == "api_call":
            results = await self._execute_api_tasks(tasks, timeout)
        elif task_type == "neural_inference":
            results = await self._execute_neural_tasks(tasks, timeout)
        elif task_type == "data_processing":
            results = await self._execute_data_tasks(tasks, timeout)
        else:
            results = await self._execute_general_tasks(tasks, priority, timeout)
        
        # Update metrics
        execution_time = time.time() - start_time
        self._update_metrics(len(tasks), execution_time, results)
        
        return results
    
    async def _execute_api_tasks(self, tasks: List[Callable], timeout: float) -> List[Any]:
        """Execute API tasks with connection pooling optimization"""
        # Group tasks by endpoint for batching
        endpoint_groups = defaultdict(list)
        
        for i, task in enumerate(tasks):
            endpoint = getattr(task, '_endpoint', 'default')
            endpoint_groups[endpoint].append((i, task))
        
        # Execute groups concurrently
        all_results = [None] * len(tasks)
        
        async def execute_group(endpoint, group_tasks):
            # Use semaphore to limit concurrent API calls per endpoint
            semaphore = asyncio.Semaphore(5)  # Max 5 concurrent calls per endpoint
            
            async def execute_single(index, task):
                async with semaphore:
                    try:
                        if asyncio.iscoroutinefunction(task):
                            result = await asyncio.wait_for(task(), timeout=timeout/len(group_tasks))
                        else:
                            loop = asyncio.get_event_loop()
                            result = await loop.run_in_executor(
                                self.thread_pool, task
                            )
                        return index, result
                    except Exception as e:
                        logger.error(f"API task failed: {e}")
                        return index, None
            
            group_results = await asyncio.gather(
                *[execute_single(idx, task) for idx, task in group_tasks],
                return_exceptions=True
            )
            
            for idx, result in group_results:
                if not isinstance(result, Exception):
                    all_results[idx] = result
        
        # Execute all endpoint groups
        await asyncio.gather(
            *[execute_group(endpoint, group) for endpoint, group in endpoint_groups.items()],
            return_exceptions=True
        )
        
        return all_results
    
    async def _execute_neural_tasks(self, tasks: List[Callable], timeout: float) -> List[Any]:
        """Execute neural inference tasks with GPU optimization"""
        if self.enable_gpu and self._gpu_available():
            # Batch neural tasks for GPU efficiency
            return await self._execute_gpu_batched(tasks, timeout)
        else:
            # Use CPU with thread pool
            return await self._execute_cpu_parallel(tasks, timeout)
    
    async def _execute_data_tasks(self, tasks: List[Callable], timeout: float) -> List[Any]:
        """Execute data processing tasks with process pool for CPU-intensive work"""
        try:
            loop = asyncio.get_event_loop()
            
            # Submit tasks to process pool
            futures = []
            for task in tasks:
                if asyncio.iscoroutinefunction(task):
                    # Convert async task to sync for process pool
                    sync_task = lambda: asyncio.run(task())
                    future = loop.run_in_executor(self.process_pool, sync_task)
                else:
                    future = loop.run_in_executor(self.process_pool, task)
                futures.append(future)
            
            # Wait for completion with timeout
            results = await asyncio.wait_for(
                asyncio.gather(*futures, return_exceptions=True),
                timeout=timeout
            )
            
            return results
            
        except asyncio.TimeoutError:
            logger.warning(f"Data processing tasks timed out after {timeout}s")
            return [None] * len(tasks)
    
    async def _execute_general_tasks(self, tasks: List[Callable], priority: int, 
                                   timeout: float) -> List[Any]:
        """Execute general tasks with priority-based routing"""
        # Route to appropriate queue based on priority
        if priority >= 3:
            queue = self.high_priority_queue
        elif priority == 2:
            queue = self.medium_priority_queue
        else:
            queue = self.low_priority_queue
        
        # Submit tasks to queue
        task_ids = []
        for i, task in enumerate(tasks):
            task_id = f"task_{time.time()}_{i}"
            await queue.put((task_id, task, time.time()))
            task_ids.append(task_id)
        
        # Wait for results
        results = []
        start_time = time.time()
        
        while len(results) < len(tasks) and (time.time() - start_time) < timeout:
            # Process tasks from queues
            await self._process_queue_batch()
            await asyncio.sleep(0.001)  # Small delay to prevent CPU spinning
        
        return results
    
    async def _execute_gpu_batched(self, tasks: List[Callable], timeout: float) -> List[Any]:
        """Execute tasks in GPU batches for maximum efficiency"""
        try:
            import torch
            
            # Group tasks into optimal batch sizes
            batch_size = 32  # Optimal for most GPUs
            batches = [tasks[i:i+batch_size] for i in range(0, len(tasks), batch_size)]
            
            all_results = []
            
            for batch in batches:
                batch_start = time.time()
                
                # Execute batch on GPU
                with torch.cuda.device(0):  # Use first GPU
                    batch_results = await asyncio.gather(
                        *[self._execute_single_gpu_task(task) for task in batch],
                        return_exceptions=True
                    )
                
                all_results.extend(batch_results)
                
                # Check timeout
                if time.time() - batch_start > timeout / len(batches):
                    logger.warning("GPU batch execution timeout")
                    break
            
            return all_results
            
        except ImportError:
            logger.warning("PyTorch not available, falling back to CPU")
            return await self._execute_cpu_parallel(tasks, timeout)
    
    async def _execute_single_gpu_task(self, task: Callable) -> Any:
        """Execute single task on GPU"""
        try:
            if asyncio.iscoroutinefunction(task):
                return await task()
            else:
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, task)
        except Exception as e:
            logger.error(f"GPU task execution failed: {e}")
            return None
    
    async def _execute_cpu_parallel(self, tasks: List[Callable], timeout: float) -> List[Any]:
        """Execute tasks in parallel on CPU"""
        try:
            # Use thread pool for I/O bound tasks
            futures = []
            loop = asyncio.get_event_loop()
            
            for task in tasks:
                if asyncio.iscoroutinefunction(task):
                    future = task()
                else:
                    future = loop.run_in_executor(self.thread_pool, task)
                futures.append(future)
            
            results = await asyncio.wait_for(
                asyncio.gather(*futures, return_exceptions=True),
                timeout=timeout
            )
            
            return results
            
        except asyncio.TimeoutError:
            logger.warning(f"CPU parallel execution timed out after {timeout}s")
            return [None] * len(tasks)
    
    async def _process_queue_batch(self):
        """Process a batch of tasks from priority queues"""
        # Process high priority first
        await self._process_single_queue(self.high_priority_queue, "high")
        await self._process_single_queue(self.medium_priority_queue, "medium")
        await self._process_single_queue(self.low_priority_queue, "low")
    
    async def _process_single_queue(self, queue: asyncio.Queue, priority: str):
        """Process tasks from a single priority queue"""
        processed = 0
        max_batch = 10  # Process up to 10 tasks per batch
        
        while processed < max_batch and not queue.empty():
            try:
                task_id, task, submit_time = await asyncio.wait_for(
                    queue.get(), timeout=0.001
                )
                
                # Execute task
                start_time = time.time()
                try:
                    if asyncio.iscoroutinefunction(task):
                        result = await task()
                    else:
                        loop = asyncio.get_event_loop()
                        result = await loop.run_in_executor(self.thread_pool, task)
                    
                    execution_time = time.time() - start_time
                    self._record_task_completion(task_id, execution_time, True)
                    
                except Exception as e:
                    execution_time = time.time() - start_time
                    self._record_task_completion(task_id, execution_time, False)
                    logger.error(f"Task {task_id} failed: {e}")
                
                processed += 1
                
            except asyncio.TimeoutError:
                break  # No more tasks in queue
    
    def _record_task_completion(self, task_id: str, execution_time: float, success: bool):
        """Record task completion metrics"""
        self.metrics.total_tasks += 1
        if success:
            self.metrics.completed_tasks += 1
        else:
            self.metrics.failed_tasks += 1
        
        # Update average execution time
        if self.metrics.avg_execution_time == 0:
            self.metrics.avg_execution_time = execution_time
        else:
            alpha = 0.1  # Exponential moving average
            self.metrics.avg_execution_time = (
                alpha * execution_time + (1 - alpha) * self.metrics.avg_execution_time
            )
        
        self.metrics.total_execution_time += execution_time
        
        # Record in history
        self.execution_history.append({
            'task_id': task_id,
            'execution_time': execution_time,
            'success': success,
            'timestamp': time.time()
        })
    
    async def _start_specialized_processors(self):
        """Start specialized processors for different task types"""
        # API processor with connection pooling
        self.api_processor = asyncio.create_task(self._api_processor_worker())
        
        # Neural processor with GPU optimization
        if self.enable_gpu:
            self.neural_processor = asyncio.create_task(self._neural_processor_worker())
        
        # Data processor with process pool
        self.data_processor = asyncio.create_task(self._data_processor_worker())
    
    async def _api_processor_worker(self):
        """Specialized worker for API tasks"""
        while self.running:
            try:
                # Process API tasks with rate limiting
                await asyncio.sleep(0.01)  # 100 tasks/second max
            except Exception as e:
                logger.error(f"API processor error: {e}")
    
    async def _neural_processor_worker(self):
        """Specialized worker for neural inference tasks"""
        while self.running:
            try:
                # Process neural tasks with GPU batching
                await asyncio.sleep(0.001)  # High frequency for low latency
            except Exception as e:
                logger.error(f"Neural processor error: {e}")
    
    async def _data_processor_worker(self):
        """Specialized worker for data processing tasks"""
        while self.running:
            try:
                # Process data tasks with CPU optimization
                await asyncio.sleep(0.1)  # Lower frequency for heavy tasks
            except Exception as e:
                logger.error(f"Data processor error: {e}")
    
    async def _monitor_workers(self):
        """Monitor worker performance and adjust load balancing"""
        while self.running:
            try:
                # Update system metrics
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent
                
                # Log performance metrics
                if self.metrics.total_tasks > 0:
                    logger.debug(
                        f"🚀 [PARALLEL] Tasks: {self.metrics.completed_tasks}/{self.metrics.total_tasks} "
                        f"({self.metrics.success_rate:.1f}% success), "
                        f"Avg time: {self.metrics.avg_execution_time*1000:.1f}ms, "
                        f"CPU: {cpu_percent:.1f}%, Memory: {memory_percent:.1f}%"
                    )
                
                await asyncio.sleep(30)  # Monitor every 30 seconds
                
            except Exception as e:
                logger.error(f"Worker monitoring error: {e}")
    
    def _update_metrics(self, task_count: int, execution_time: float, results: List[Any]):
        """Update overall metrics"""
        successful_tasks = sum(1 for r in results if r is not None)
        failed_tasks = task_count - successful_tasks
        
        self.metrics.total_tasks += task_count
        self.metrics.completed_tasks += successful_tasks
        self.metrics.failed_tasks += failed_tasks
        self.metrics.total_execution_time += execution_time
    
    def _gpu_available(self) -> bool:
        """Check if GPU is available"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        return {
            'metrics': {
                'total_tasks': self.metrics.total_tasks,
                'completed_tasks': self.metrics.completed_tasks,
                'failed_tasks': self.metrics.failed_tasks,
                'success_rate': self.metrics.success_rate,
                'avg_execution_time_ms': self.metrics.avg_execution_time * 1000,
                'throughput_tasks_per_sec': self.metrics.throughput
            },
            'system': {
                'max_workers': self.max_workers,
                'gpu_enabled': self.enable_gpu,
                'gpu_available': self._gpu_available(),
                'cpu_count': psutil.cpu_count(),
                'memory_gb': psutil.virtual_memory().total / (1024**3)
            },
            'queues': {
                'high_priority': self.high_priority_queue.qsize(),
                'medium_priority': self.medium_priority_queue.qsize(),
                'low_priority': self.low_priority_queue.qsize()
            }
        }

# Global parallel processor instance
parallel_processor = ParallelProcessor()

async def parallel_execute(tasks: List[Callable], task_type: str = "general", 
                          priority: int = 2, timeout: float = 30.0) -> List[Any]:
    """Execute tasks in parallel using the global processor"""
    if not parallel_processor.running:
        await parallel_processor.start()
    
    return await parallel_processor.execute_parallel(tasks, task_type, priority, timeout)

logger.info("🚀 [PARALLEL] Parallel processing module loaded")
