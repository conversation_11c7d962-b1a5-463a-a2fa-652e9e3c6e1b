"""
Advanced Temporal Intelligence System for AutoGPT-Trader
State-of-the-art time awareness with multi-scale temporal analysis
"""

import asyncio
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import math
import time
from concurrent.futures import ThreadPoolExecutor
import threading
try:
    import scipy.stats
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    logger.warning("SciPy not available - some statistical validations will be simplified")

logger = logging.getLogger(__name__)

class TemporalScale(Enum):
    """Multi-scale temporal analysis levels"""
    MICROSECOND = "microsecond"  # Order execution timing
    MILLISECOND = "millisecond"  # High-frequency patterns
    SECOND = "second"           # Tick-by-tick analysis
    MINUTE = "minute"           # Short-term patterns
    HOUR = "hour"              # Intraday cycles
    DAY = "day"                # Daily patterns
    WEEK = "week"              # Weekly cycles
    MONTH = "month"            # Seasonal trends
    QUARTER = "quarter"        # Long-term cycles
    YEAR = "year"              # Macro trends

class MarketSession(Enum):
    """Global market sessions with precise timing"""
    SYDNEY = "sydney"
    TOKYO = "tokyo"
    LONDON = "london"
    NEW_YORK = "new_york"
    OVERLAP_TOKYO_LONDON = "tokyo_london_overlap"
    OVERLAP_LONDON_NY = "london_ny_overlap"
    WEEKEND = "weekend"
    HOLIDAY = "holiday"

@dataclass
class TemporalPattern:
    """Represents a discovered temporal pattern"""
    pattern_id: str
    scale: TemporalScale
    pattern_type: str  # 'cyclical', 'trend', 'volatility', 'volume', 'price'
    strength: float  # 0.0 to 1.0
    confidence: float  # Statistical confidence
    frequency: float  # Pattern frequency (cycles per unit)
    phase: float  # Current phase in cycle
    amplitude: float  # Pattern amplitude
    discovered_at: datetime
    last_validated: datetime
    validation_count: int = 0
    profit_correlation: float = 0.0
    market_conditions: Dict[str, Any] = field(default_factory=dict)

@dataclass
class TemporalContext:
    """Complete temporal context for trading decisions"""
    timestamp: datetime
    market_session: MarketSession
    session_progress: float  # 0.0 to 1.0 through session
    time_to_session_end: timedelta
    next_session: MarketSession
    time_to_next_session: timedelta
    
    # Multi-scale time factors
    microsecond_factor: float
    millisecond_factor: float
    second_factor: float
    minute_factor: float
    hour_factor: float
    day_factor: float
    week_factor: float
    month_factor: float
    
    # Pattern analysis
    active_patterns: List[TemporalPattern]
    pattern_confluence: float  # Multiple patterns aligning
    temporal_momentum: float  # Rate of change in time factors
    
    # Execution timing
    optimal_execution_window: Tuple[datetime, datetime]
    execution_urgency: float  # 0.0 to 1.0
    latency_budget: float  # Available time for execution (ms)

class AdvancedTemporalIntelligence:
    """
    State-of-the-art temporal intelligence system
    Provides comprehensive time awareness for trading decisions
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        
        # Pattern discovery and storage
        self.discovered_patterns: Dict[str, TemporalPattern] = {}
        self.pattern_history: deque = deque(maxlen=10000)
        self.pattern_performance: Dict[str, List[float]] = defaultdict(list)
        
        # Multi-scale analyzers
        self.scale_analyzers = {
            scale: TemporalScaleAnalyzer(scale) for scale in TemporalScale
        }
        
        # Market session tracker
        self.session_tracker = MarketSessionTracker()
        
        # Temporal neural networks
        self.temporal_networks = {}
        self._initialize_temporal_networks()
        
        # Performance tracking
        self.temporal_performance = {
            'pattern_accuracy': deque(maxlen=1000),
            'timing_precision': deque(maxlen=1000),
            'profit_correlation': deque(maxlen=1000)
        }
        
        # Real-time state
        self.current_context: Optional[TemporalContext] = None
        self.last_update: Optional[datetime] = None
        
        # Threading for real-time updates
        self.update_thread: Optional[threading.Thread] = None
        self.running = False
        
        logger.info("🕐 [TEMPORAL] Advanced Temporal Intelligence System initialized")
    
    def _initialize_temporal_networks(self):
        """Initialize specialized temporal neural networks"""
        try:
            # Temporal Convolutional Network for pattern recognition
            self.temporal_networks['tcn'] = TemporalConvolutionalNetwork(
                input_channels=20,
                output_size=10,
                num_channels=[64, 128, 256],
                kernel_size=3,
                dropout=0.1
            )
            
            # Temporal Attention Network for importance weighting
            self.temporal_networks['attention'] = TemporalAttentionNetwork(
                input_dim=256,
                hidden_dim=512,
                num_heads=8,
                num_layers=4
            )
            
            # Time-Series Transformer for sequence modeling
            self.temporal_networks['transformer'] = TimeSeriesTransformer(
                d_model=256,
                nhead=8,
                num_layers=6,
                sequence_length=100
            )
            
            logger.info("✅ [TEMPORAL] Temporal neural networks initialized")
            
        except Exception as e:
            logger.error(f"❌ [TEMPORAL] Failed to initialize neural networks: {e}")
    
    async def start_real_time_analysis(self):
        """Start real-time temporal analysis"""
        if self.running:
            return
            
        self.running = True
        self.update_thread = threading.Thread(
            target=self._real_time_update_loop,
            daemon=True
        )
        self.update_thread.start()
        logger.info("🚀 [TEMPORAL] Real-time temporal analysis started")
    
    def _real_time_update_loop(self):
        """Real-time update loop for temporal context"""
        while self.running:
            try:
                # Update temporal context every 100ms for high precision
                asyncio.run(self._update_temporal_context())
                time.sleep(0.1)  # 100ms updates
                
            except Exception as e:
                logger.error(f"❌ [TEMPORAL] Real-time update error: {e}")
                time.sleep(1.0)  # Longer sleep on error
    
    async def stop_real_time_analysis(self):
        """Stop real-time temporal analysis"""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=5.0)
        logger.info("🛑 [TEMPORAL] Real-time temporal analysis stopped")

    async def get_temporal_context(self) -> TemporalContext:
        """Get current temporal context for trading decisions"""
        if not self.current_context or self._context_needs_update():
            await self._update_temporal_context()
        return self.current_context
    
    def _context_needs_update(self) -> bool:
        """Check if temporal context needs updating"""
        if not self.last_update:
            return True
        
        # Update every 100ms for high precision
        time_since_update = (datetime.now(timezone.utc) - self.last_update).total_seconds()
        return time_since_update > 0.1

    async def _update_temporal_context(self):
        """Update the current temporal context"""
        try:
            current_time = datetime.now(timezone.utc)
            
            # Get market session information
            session_info = await self.session_tracker.get_current_session(current_time)
            
            # Calculate multi-scale time factors
            time_factors = await self._calculate_multi_scale_factors(current_time)
            
            # Analyze active patterns
            active_patterns = await self._analyze_active_patterns(current_time)
            
            # Calculate execution timing
            execution_timing = await self._calculate_execution_timing(current_time, active_patterns)
            
            # Create temporal context
            self.current_context = TemporalContext(
                timestamp=current_time,
                market_session=session_info['session'],
                session_progress=session_info['progress'],
                time_to_session_end=session_info['time_to_end'],
                next_session=session_info['next_session'],
                time_to_next_session=session_info['time_to_next'],
                **time_factors,
                active_patterns=active_patterns,
                pattern_confluence=self._calculate_pattern_confluence(active_patterns),
                temporal_momentum=self._calculate_temporal_momentum(),
                **execution_timing
            )
            
            self.last_update = current_time
            
        except Exception as e:
            logger.error(f"❌ [TEMPORAL] Failed to update temporal context: {e}")

    async def discover_temporal_patterns(self, market_data: pd.DataFrame, 
                                       symbol: str) -> List[TemporalPattern]:
        """Discover new temporal patterns in market data"""
        patterns = []
        
        try:
            # Multi-scale pattern discovery
            for scale in TemporalScale:
                scale_patterns = await self.scale_analyzers[scale].discover_patterns(
                    market_data, symbol
                )
                patterns.extend(scale_patterns)
            
            # Validate and store patterns
            for pattern in patterns:
                if await self._validate_pattern(pattern, market_data):
                    self.discovered_patterns[pattern.pattern_id] = pattern
                    self.pattern_history.append(pattern)
            
            logger.info(f"🔍 [TEMPORAL] Discovered {len(patterns)} patterns for {symbol}")
            return patterns
            
        except Exception as e:
            logger.error(f"❌ [TEMPORAL] Pattern discovery failed: {e}")
            return []

    async def predict_optimal_timing(self, symbol: str, action: str, 
                                   market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Predict optimal timing for trade execution"""
        try:
            context = await self.get_temporal_context()
            
            # Neural network prediction
            timing_prediction = await self._neural_timing_prediction(
                symbol, action, market_data, context
            )
            
            # Pattern-based timing
            pattern_timing = await self._pattern_based_timing(
                symbol, action, context.active_patterns
            )
            
            # Session-based timing
            session_timing = await self._session_based_timing(
                action, context.market_session, context.session_progress
            )
            
            # Combine predictions
            optimal_timing = self._combine_timing_predictions(
                timing_prediction, pattern_timing, session_timing
            )
            
            return optimal_timing
            
        except Exception as e:
            logger.error(f"❌ [TEMPORAL] Timing prediction failed: {e}")
            return {'execute_now': True, 'confidence': 0.5}

    async def _calculate_multi_scale_factors(self, current_time_or_market_data, timeframes=None) -> Dict[str, float]:
        """
        Calculate time factors across all scales with optional market data enhancement

        Args:
            current_time_or_market_data: Either datetime object (legacy) or Dict with market data
            timeframes: Optional list of timeframes for market-data-aware analysis

        Returns:
            Dict[str, float]: Multi-scale temporal factors
        """
        # Backward compatibility: if first argument is datetime, use legacy method
        if isinstance(current_time_or_market_data, datetime):
            return await self._calculate_time_based_factors(current_time_or_market_data)

        # Enhanced method: market-data-aware multi-scale factors
        market_data = current_time_or_market_data
        timeframes = timeframes or ['1m', '5m', '15m', '1h', '4h', '1d']
        current_time = datetime.now(timezone.utc)

        # Start with time-based factors
        factors = await self._calculate_time_based_factors(current_time)

        # Enhance with market-data-aware factors
        market_factors = await self._calculate_market_aware_factors(market_data, timeframes, current_time)

        # Combine time-based and market-based factors
        enhanced_factors = self._combine_temporal_factors(factors, market_factors)

        return enhanced_factors

    async def _calculate_time_based_factors(self, current_time: datetime) -> Dict[str, float]:
        """Calculate traditional time-based factors (legacy method)"""
        factors = {}

        # Microsecond factor (for order execution timing)
        microsecond = current_time.microsecond
        factors['microsecond_factor'] = math.sin(2 * math.pi * microsecond / 1000000) * 0.1 + 0.9

        # Millisecond factor (for high-frequency patterns)
        millisecond = (current_time.microsecond // 1000) + (current_time.second * 1000)
        factors['millisecond_factor'] = math.sin(2 * math.pi * millisecond / 60000) * 0.2 + 0.8

        # Second factor (for tick-by-tick analysis)
        second = current_time.second
        factors['second_factor'] = math.sin(2 * math.pi * second / 60) * 0.3 + 0.7

        # Minute factor (for short-term patterns)
        minute = current_time.minute
        factors['minute_factor'] = math.sin(2 * math.pi * minute / 60) * 0.4 + 0.6

        # Hour factor (for intraday cycles)
        hour = current_time.hour
        # Peak trading hours get higher factors
        if 8 <= hour <= 16:  # London/NY overlap
            base_factor = 0.9
        elif 0 <= hour <= 8 or 16 <= hour <= 24:
            base_factor = 0.6
        else:
            base_factor = 0.4
        factors['hour_factor'] = base_factor + math.sin(2 * math.pi * hour / 24) * 0.1

        # Day factor (for daily patterns)
        day_of_week = current_time.weekday()
        # Weekdays get higher factors than weekends
        if day_of_week < 5:  # Monday-Friday
            factors['day_factor'] = 0.8 + math.sin(2 * math.pi * day_of_week / 7) * 0.2
        else:  # Weekend
            factors['day_factor'] = 0.3

        # Week factor (for weekly cycles)
        week_of_year = current_time.isocalendar()[1]
        factors['week_factor'] = 0.7 + math.sin(2 * math.pi * week_of_year / 52) * 0.3

        # Month factor (for seasonal trends)
        month = current_time.month
        factors['month_factor'] = 0.6 + math.sin(2 * math.pi * month / 12) * 0.4

        return factors

    async def _calculate_market_aware_factors(self, market_data: Dict, timeframes: List[str], current_time: datetime) -> Dict[str, float]:
        """Calculate market-data-aware temporal factors for enhanced profit generation"""
        try:
            market_factors = {}

            # Extract market metrics
            current_price = float(market_data.get('price', 0))
            volume_24h = float(market_data.get('volume_24h', 0))
            price_change_24h = float(market_data.get('price_change_24h', 0))
            volatility = abs(price_change_24h) / 100.0 if price_change_24h else 0.01

            # Calculate volatility-adjusted scaling factors for each timeframe
            for timeframe in timeframes:
                timeframe_factor = self._calculate_timeframe_factor(
                    timeframe, volatility, volume_24h, current_price, current_time
                )
                market_factors[f'{timeframe}_factor'] = timeframe_factor

            # Calculate momentum indicators
            momentum_strength = self._calculate_momentum_strength(market_data)
            market_factors['momentum_factor'] = momentum_strength

            # Calculate trend strength indicators
            trend_strength = self._calculate_trend_strength(market_data)
            market_factors['trend_factor'] = trend_strength

            # Calculate liquidity-based factors
            liquidity_factor = self._calculate_liquidity_factor(market_data)
            market_factors['liquidity_factor'] = liquidity_factor

            # Calculate volatility-adjusted execution timing
            execution_factor = self._calculate_execution_timing_factor(volatility, momentum_strength)
            market_factors['execution_factor'] = execution_factor

            return market_factors

        except Exception as e:
            logger.error(f"❌ [TEMPORAL] Market-aware factor calculation failed: {e}")
            # Return neutral factors on error
            return {f'{tf}_factor': 0.5 for tf in timeframes}

    def _calculate_timeframe_factor(self, timeframe: str, volatility: float, volume: float, price: float, current_time: datetime) -> float:
        """Calculate volatility-adjusted scaling factor for specific timeframe"""
        try:
            # Base factor from timeframe characteristics
            timeframe_weights = {
                '1m': 0.9,   # High weight for micro-trading
                '5m': 0.8,   # Good for short-term opportunities
                '15m': 0.7,  # Medium-term patterns
                '1h': 0.6,   # Hourly trends
                '4h': 0.5,   # Longer-term analysis
                '1d': 0.4    # Daily patterns
            }

            base_factor = timeframe_weights.get(timeframe, 0.5)

            # Volatility adjustment (higher volatility = higher opportunity)
            volatility_multiplier = 1.0 + min(volatility * 2.0, 1.0)  # Cap at 2x

            # Volume adjustment (higher volume = better execution)
            volume_multiplier = 1.0 + min(math.log10(max(volume, 1)) / 10.0, 0.5)

            # Time-of-day adjustment for intraday timeframes
            if timeframe in ['1m', '5m', '15m']:
                hour = current_time.hour
                if 8 <= hour <= 16:  # Peak trading hours
                    time_multiplier = 1.2
                elif 0 <= hour <= 8 or 16 <= hour <= 24:
                    time_multiplier = 1.0
                else:
                    time_multiplier = 0.8
            else:
                time_multiplier = 1.0

            # Combine factors
            final_factor = base_factor * volatility_multiplier * volume_multiplier * time_multiplier

            # Ensure factor stays within reasonable bounds for aggressive micro-trading
            return max(0.1, min(2.0, final_factor))

        except Exception as e:
            logger.debug(f"⚠️ [TEMPORAL] Timeframe factor calculation error for {timeframe}: {e}")
            return 0.5

    def _calculate_momentum_strength(self, market_data: Dict) -> float:
        """Calculate momentum strength from market data"""
        try:
            price_change_24h = float(market_data.get('price_change_24h', 0))
            volume_change = float(market_data.get('volume_change_24h', 0))

            # Momentum based on price and volume changes
            price_momentum = abs(price_change_24h) / 100.0
            volume_momentum = abs(volume_change) / 100.0 if volume_change else 0

            # Combined momentum with price weighted higher
            momentum = (price_momentum * 0.7 + volume_momentum * 0.3)

            # Normalize to 0.1-1.5 range for aggressive trading
            return max(0.1, min(1.5, momentum))

        except Exception as e:
            logger.debug(f"⚠️ [TEMPORAL] Momentum calculation error: {e}")
            return 0.5

    def _calculate_trend_strength(self, market_data: Dict) -> float:
        """Calculate trend strength indicators"""
        try:
            price_change_24h = float(market_data.get('price_change_24h', 0))
            price_change_7d = float(market_data.get('price_change_7d', 0))

            # Trend consistency (same direction over different periods)
            if price_change_24h != 0 and price_change_7d != 0:
                trend_consistency = 1.0 if (price_change_24h > 0) == (price_change_7d > 0) else 0.3
            else:
                trend_consistency = 0.5

            # Trend magnitude
            trend_magnitude = (abs(price_change_24h) + abs(price_change_7d)) / 200.0

            # Combined trend strength
            trend_strength = trend_consistency * (1.0 + trend_magnitude)

            return max(0.1, min(1.5, trend_strength))

        except Exception as e:
            logger.debug(f"⚠️ [TEMPORAL] Trend strength calculation error: {e}")
            return 0.5

    def _calculate_liquidity_factor(self, market_data: Dict) -> float:
        """Calculate liquidity-based factor for execution timing"""
        try:
            volume_24h = float(market_data.get('volume_24h', 0))
            spread = float(market_data.get('spread', 0.001))

            # Higher volume = better liquidity
            volume_factor = min(1.0, math.log10(max(volume_24h, 1)) / 8.0)

            # Lower spread = better liquidity
            spread_factor = max(0.1, 1.0 - min(spread * 1000, 0.9))

            # Combined liquidity factor
            liquidity = (volume_factor * 0.6 + spread_factor * 0.4)

            return max(0.1, min(1.5, liquidity))

        except Exception as e:
            logger.debug(f"⚠️ [TEMPORAL] Liquidity factor calculation error: {e}")
            return 0.5

    def _calculate_execution_timing_factor(self, volatility: float, momentum: float) -> float:
        """Calculate optimal execution timing factor"""
        try:
            # Higher volatility and momentum = more urgent execution
            urgency = (volatility + momentum) / 2.0

            # Convert to execution factor (higher urgency = higher factor)
            execution_factor = 0.5 + urgency * 0.5

            return max(0.1, min(1.5, execution_factor))

        except Exception as e:
            logger.debug(f"⚠️ [TEMPORAL] Execution timing calculation error: {e}")
            return 0.5

    def _combine_temporal_factors(self, time_factors: Dict[str, float], market_factors: Dict[str, float]) -> Dict[str, float]:
        """Combine time-based and market-based factors for enhanced profit generation"""
        try:
            combined_factors = time_factors.copy()

            # Enhance time factors with market data
            for key, time_factor in time_factors.items():
                # Apply market momentum and trend adjustments
                momentum_adj = market_factors.get('momentum_factor', 1.0)
                trend_adj = market_factors.get('trend_factor', 1.0)
                liquidity_adj = market_factors.get('liquidity_factor', 1.0)

                # Weighted combination favoring market conditions for profit maximization
                enhanced_factor = time_factor * (0.4 + 0.3 * momentum_adj + 0.2 * trend_adj + 0.1 * liquidity_adj)
                combined_factors[key] = max(0.1, min(2.0, enhanced_factor))

            # Add market-specific factors
            combined_factors.update(market_factors)

            return combined_factors

        except Exception as e:
            logger.error(f"❌ [TEMPORAL] Factor combination failed: {e}")
            return time_factors

    async def _analyze_active_patterns(self, current_time: datetime) -> List[TemporalPattern]:
        """Analyze currently active temporal patterns"""
        active_patterns = []

        for pattern in self.discovered_patterns.values():
            # Check if pattern is still valid
            age = (current_time - pattern.discovered_at).total_seconds()
            if age > 86400:  # 24 hours
                continue

            # Calculate current phase
            if pattern.frequency > 0:
                time_since_discovery = (current_time - pattern.discovered_at).total_seconds()
                current_phase = (time_since_discovery * pattern.frequency) % (2 * math.pi)
                pattern.phase = current_phase

                # Check if pattern is in active phase
                phase_strength = abs(math.sin(current_phase))
                if phase_strength > 0.5:  # Pattern is active
                    active_patterns.append(pattern)

        return active_patterns

    def _calculate_pattern_confluence(self, patterns: List[TemporalPattern]) -> float:
        """Calculate confluence score when multiple patterns align"""
        if len(patterns) < 2:
            return 0.0

        # Calculate phase alignment
        phases = [p.phase for p in patterns]
        phase_variance = np.var(phases)

        # Calculate strength alignment
        strengths = [p.strength for p in patterns]
        avg_strength = np.mean(strengths)

        # Confluence score
        confluence = avg_strength * (1.0 - phase_variance / (2 * math.pi))
        return max(0.0, min(1.0, confluence))

    def _calculate_temporal_momentum(self) -> float:
        """Calculate rate of change in temporal factors"""
        if len(self.pattern_history) < 2:
            return 0.0

        recent_patterns = list(self.pattern_history)[-10:]
        if len(recent_patterns) < 2:
            return 0.0

        # Calculate momentum based on pattern strength changes
        strength_changes = []
        for i in range(1, len(recent_patterns)):
            prev_strength = recent_patterns[i-1].strength
            curr_strength = recent_patterns[i].strength
            strength_changes.append(curr_strength - prev_strength)

        return np.mean(strength_changes) if strength_changes else 0.0

    async def _calculate_execution_timing(self, current_time: datetime,
                                        patterns: List[TemporalPattern]) -> Dict[str, Any]:
        """Calculate optimal execution timing"""
        # Default execution window (immediate)
        window_start = current_time
        window_end = current_time + timedelta(seconds=30)

        # Adjust based on active patterns
        if patterns:
            # Find patterns that suggest delayed execution
            delay_patterns = [p for p in patterns if p.pattern_type in ['volatility', 'volume']]
            if delay_patterns:
                avg_delay = np.mean([p.amplitude for p in delay_patterns])
                window_start = current_time + timedelta(seconds=avg_delay)
                window_end = window_start + timedelta(seconds=30)

        # Calculate execution urgency
        urgency = 0.5  # Default
        if patterns:
            # Higher urgency if patterns are expiring soon
            time_to_expiry = [
                (p.discovered_at + timedelta(hours=1) - current_time).total_seconds()
                for p in patterns
            ]
            min_time_to_expiry = min(time_to_expiry) if time_to_expiry else 3600
            urgency = max(0.0, min(1.0, 1.0 - min_time_to_expiry / 3600))

        # Calculate latency budget
        latency_budget = 100.0  # Default 100ms
        if urgency > 0.8:
            latency_budget = 50.0  # Tighter budget for urgent trades
        elif urgency < 0.3:
            latency_budget = 200.0  # More relaxed for non-urgent trades

        return {
            'optimal_execution_window': (window_start, window_end),
            'execution_urgency': urgency,
            'latency_budget': latency_budget
        }


class TemporalConvolutionalNetwork(nn.Module):
    """Temporal Convolutional Network for pattern recognition"""

    def __init__(self, input_channels: int, output_size: int,
                 num_channels: List[int], kernel_size: int = 3, dropout: float = 0.1):
        super().__init__()

        layers = []
        num_levels = len(num_channels)

        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = input_channels if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]

            layers.append(
                TemporalBlock(
                    in_channels, out_channels, kernel_size,
                    stride=1, dilation=dilation_size, padding=(kernel_size-1) * dilation_size,
                    dropout=dropout
                )
            )

        self.network = nn.Sequential(*layers)
        self.linear = nn.Linear(num_channels[-1], output_size)

    def forward(self, x):
        """Forward pass through TCN"""
        y = self.network(x)
        return self.linear(y[:, :, -1])  # Take last timestep


class TemporalBlock(nn.Module):
    """Individual temporal block for TCN"""

    def __init__(self, in_channels: int, out_channels: int, kernel_size: int,
                 stride: int, dilation: int, padding: int, dropout: float = 0.1):
        super().__init__()

        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size,
                              stride=stride, padding=padding, dilation=dilation)
        self.chomp1 = Chomp1d(padding)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)

        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size,
                              stride=stride, padding=padding, dilation=dilation)
        self.chomp2 = Chomp1d(padding)
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)

        self.net = nn.Sequential(
            self.conv1, self.chomp1, self.relu1, self.dropout1,
            self.conv2, self.chomp2, self.relu2, self.dropout2
        )

        self.downsample = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else None
        self.relu = nn.ReLU()

    def forward(self, x):
        out = self.net(x)
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)


class Chomp1d(nn.Module):
    """Chomp layer to ensure causal convolutions"""

    def __init__(self, chomp_size: int):
        super().__init__()
        self.chomp_size = chomp_size

    def forward(self, x):
        return x[:, :, :-self.chomp_size].contiguous()


class TemporalAttentionNetwork(nn.Module):
    """Temporal attention network for importance weighting"""

    def __init__(self, input_dim: int, hidden_dim: int, num_heads: int, num_layers: int):
        super().__init__()

        self.input_projection = nn.Linear(input_dim, hidden_dim)

        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=0.1,
            batch_first=True
        )

        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)
        self.temporal_attention = nn.MultiheadAttention(hidden_dim, num_heads, batch_first=True)
        self.output_projection = nn.Linear(hidden_dim, 1)

    def forward(self, x, temporal_mask=None):
        """Forward pass with temporal attention"""
        # Project input
        x = self.input_projection(x)

        # Transformer encoding
        encoded = self.transformer(x)

        # Temporal attention
        attended, attention_weights = self.temporal_attention(encoded, encoded, encoded, attn_mask=temporal_mask)

        # Output projection
        output = self.output_projection(attended)

        return output, attention_weights


class TimeSeriesTransformer(nn.Module):
    """Specialized transformer for time series analysis"""

    def __init__(self, d_model: int, nhead: int, num_layers: int, sequence_length: int):
        super().__init__()

        self.d_model = d_model
        self.sequence_length = sequence_length

        # Positional encoding for time series
        self.positional_encoding = TemporalPositionalEncoding(d_model, sequence_length)

        # Transformer layers
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=0.1,
            batch_first=True
        )

        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)

        # Output layers
        self.output_norm = nn.LayerNorm(d_model)
        self.output_linear = nn.Linear(d_model, 1)

    def forward(self, x):
        """Forward pass through time series transformer"""
        # Add positional encoding
        x = self.positional_encoding(x)

        # Transformer encoding
        encoded = self.transformer(x)

        # Output processing
        output = self.output_norm(encoded)
        output = self.output_linear(output)

        return output


class TemporalPositionalEncoding(nn.Module):
    """Positional encoding specialized for temporal data"""

    def __init__(self, d_model: int, max_length: int = 5000):
        super().__init__()

        pe = torch.zeros(max_length, d_model)
        position = torch.arange(0, max_length, dtype=torch.float).unsqueeze(1)

        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                           (-math.log(10000.0) / d_model))

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)

        self.register_buffer('pe', pe.unsqueeze(0))

    def forward(self, x):
        return x + self.pe[:, :x.size(1)]


class TemporalScaleAnalyzer:
    """Analyzer for specific temporal scales"""

    def __init__(self, scale: TemporalScale):
        self.scale = scale
        self.pattern_cache = {}

    async def discover_patterns(self, data: pd.DataFrame, symbol: str) -> List[TemporalPattern]:
        """Discover patterns at this temporal scale"""
        patterns = []

        try:
            # Scale-specific analysis
            if self.scale == TemporalScale.MICROSECOND:
                patterns.extend(await self._analyze_microsecond_patterns(data, symbol))
            elif self.scale == TemporalScale.MILLISECOND:
                patterns.extend(await self._analyze_millisecond_patterns(data, symbol))
            elif self.scale == TemporalScale.SECOND:
                patterns.extend(await self._analyze_second_patterns(data, symbol))
            elif self.scale == TemporalScale.MINUTE:
                patterns.extend(await self._analyze_minute_patterns(data, symbol))
            elif self.scale == TemporalScale.HOUR:
                patterns.extend(await self._analyze_hour_patterns(data, symbol))
            elif self.scale == TemporalScale.DAY:
                patterns.extend(await self._analyze_day_patterns(data, symbol))
            elif self.scale == TemporalScale.WEEK:
                patterns.extend(await self._analyze_week_patterns(data, symbol))
            elif self.scale == TemporalScale.MONTH:
                patterns.extend(await self._analyze_month_patterns(data, symbol))

            return patterns

        except Exception as e:
            logger.error(f"❌ [TEMPORAL] Pattern discovery failed for {self.scale.value}: {e}")
            return []

    async def _analyze_microsecond_patterns(self, data: pd.DataFrame, symbol: str) -> List[TemporalPattern]:
        """Analyze microsecond-level patterns (order execution timing)"""
        patterns = []

        # Analyze order book dynamics, bid-ask spread patterns
        if 'timestamp' in data.columns and len(data) > 100:
            # Calculate microsecond intervals
            data['timestamp'] = pd.to_datetime(data['timestamp'])
            data['microsecond_interval'] = data['timestamp'].diff().dt.total_seconds() * 1000000

            # Find execution timing patterns
            if data['microsecond_interval'].std() > 0:
                pattern = TemporalPattern(
                    pattern_id=f"{symbol}_microsecond_execution_{int(time.time())}",
                    scale=self.scale,
                    pattern_type='execution_timing',
                    strength=min(1.0, data['microsecond_interval'].std() / 1000),
                    confidence=0.8,
                    frequency=1.0 / data['microsecond_interval'].mean() if data['microsecond_interval'].mean() > 0 else 0,
                    phase=0.0,
                    amplitude=data['microsecond_interval'].std(),
                    discovered_at=datetime.now(timezone.utc),
                    last_validated=datetime.now(timezone.utc)
                )
                patterns.append(pattern)

        return patterns


class MarketSessionTracker:
    """Tracks global market sessions and overlaps"""

    def __init__(self):
        # Market session times (UTC)
        self.session_times = {
            MarketSession.SYDNEY: {'start': 21, 'end': 6},      # 21:00-06:00 UTC
            MarketSession.TOKYO: {'start': 23, 'end': 8},       # 23:00-08:00 UTC
            MarketSession.LONDON: {'start': 7, 'end': 16},      # 07:00-16:00 UTC
            MarketSession.NEW_YORK: {'start': 12, 'end': 21},   # 12:00-21:00 UTC
        }

        # Overlap periods
        self.overlaps = {
            MarketSession.OVERLAP_TOKYO_LONDON: {'start': 7, 'end': 8},   # 07:00-08:00 UTC
            MarketSession.OVERLAP_LONDON_NY: {'start': 12, 'end': 16},    # 12:00-16:00 UTC
        }

    async def get_current_session(self, timestamp: datetime) -> Dict[str, Any]:
        """Get current market session information"""
        hour = timestamp.hour
        weekday = timestamp.weekday()

        # Check for weekend
        if weekday >= 5:  # Saturday = 5, Sunday = 6
            return {
                'session': MarketSession.WEEKEND,
                'progress': 0.0,
                'time_to_end': timedelta(days=1),
                'next_session': MarketSession.SYDNEY,
                'time_to_next': self._time_to_next_session(timestamp, MarketSession.SYDNEY)
            }

        # Check overlaps first (higher priority)
        for overlap, times in self.overlaps.items():
            if self._is_in_session(hour, times):
                return {
                    'session': overlap,
                    'progress': self._calculate_session_progress(hour, times),
                    'time_to_end': self._time_to_session_end(timestamp, times),
                    'next_session': self._get_next_session(overlap),
                    'time_to_next': self._time_to_next_session(timestamp, self._get_next_session(overlap))
                }

        # Check regular sessions
        for session, times in self.session_times.items():
            if self._is_in_session(hour, times):
                return {
                    'session': session,
                    'progress': self._calculate_session_progress(hour, times),
                    'time_to_end': self._time_to_session_end(timestamp, times),
                    'next_session': self._get_next_session(session),
                    'time_to_next': self._time_to_next_session(timestamp, self._get_next_session(session))
                }

        # No active session
        return {
            'session': MarketSession.WEEKEND,
            'progress': 0.0,
            'time_to_end': timedelta(hours=1),
            'next_session': self._get_next_active_session(timestamp),
            'time_to_next': self._time_to_next_session(timestamp, self._get_next_active_session(timestamp))
        }

    def _is_in_session(self, hour: int, times: Dict[str, int]) -> bool:
        """Check if current hour is within session times"""
        start, end = times['start'], times['end']

        if start <= end:  # Same day session
            return start <= hour < end
        else:  # Cross-midnight session
            return hour >= start or hour < end

    def _calculate_session_progress(self, hour: int, times: Dict[str, int]) -> float:
        """Calculate progress through current session (0.0 to 1.0)"""
        start, end = times['start'], times['end']

        if start <= end:  # Same day session
            duration = end - start
            elapsed = hour - start
        else:  # Cross-midnight session
            duration = (24 - start) + end
            if hour >= start:
                elapsed = hour - start
            else:
                elapsed = (24 - start) + hour

        return min(1.0, max(0.0, elapsed / duration))

    def _time_to_session_end(self, timestamp: datetime, times: Dict[str, int]) -> timedelta:
        """Calculate time remaining in current session"""
        end_hour = times['end']

        # Calculate end time
        if timestamp.hour < end_hour:
            end_time = timestamp.replace(hour=end_hour, minute=0, second=0, microsecond=0)
        else:
            end_time = (timestamp + timedelta(days=1)).replace(hour=end_hour, minute=0, second=0, microsecond=0)

        return end_time - timestamp

    def _get_next_session(self, current_session: MarketSession) -> MarketSession:
        """Get the next market session"""
        session_order = [
            MarketSession.SYDNEY,
            MarketSession.TOKYO,
            MarketSession.OVERLAP_TOKYO_LONDON,
            MarketSession.LONDON,
            MarketSession.OVERLAP_LONDON_NY,
            MarketSession.NEW_YORK
        ]

        try:
            current_index = session_order.index(current_session)
            return session_order[(current_index + 1) % len(session_order)]
        except ValueError:
            return MarketSession.SYDNEY

    def _get_next_active_session(self, timestamp: datetime) -> MarketSession:
        """Get the next active trading session"""
        hour = timestamp.hour

        # Simple logic to find next session
        if hour < 7:
            return MarketSession.LONDON
        elif hour < 12:
            return MarketSession.NEW_YORK
        elif hour < 21:
            return MarketSession.SYDNEY
        else:
            return MarketSession.TOKYO

    def _time_to_next_session(self, timestamp: datetime, next_session: MarketSession) -> timedelta:
        """Calculate time to next session"""
        if next_session in self.session_times:
            start_hour = self.session_times[next_session]['start']
        elif next_session in self.overlaps:
            start_hour = self.overlaps[next_session]['start']
        else:
            return timedelta(hours=1)  # Default

        # Calculate start time
        if timestamp.hour < start_hour:
            start_time = timestamp.replace(hour=start_hour, minute=0, second=0, microsecond=0)
        else:
            start_time = (timestamp + timedelta(days=1)).replace(hour=start_hour, minute=0, second=0, microsecond=0)

        return start_time - timestamp
