#!/usr/bin/env python3
"""
HIGH-SPEED CONNECTION POOL MANAGER
Implements aggressive connection pooling and WebSocket management for speed
"""

import asyncio
import aiohttp
import websockets
import json
import time
import logging
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
import ssl
import certifi

from ..performance.speed_optimizer import fast_api_call, cached_market_data, speed_optimizer

logger = logging.getLogger(__name__)

@dataclass
class ConnectionStats:
    """Connection statistics"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    last_used: datetime = None
    connection_errors: int = 0

class HighSpeedConnectionPool:
    """High-speed connection pool with aggressive optimization"""
    
    def __init__(self, max_connections: int = 50, timeout: float = 0.2):  # OPTIMIZED: Increased connections, reduced timeout
        self.max_connections = max_connections
        self.timeout = timeout
        self.session = None
        self.websocket_connections = {}
        self.connection_stats = {}
        self.rate_limiters = {}

        # OPTIMIZED: Enhanced SSL context for maximum speed
        self.ssl_context = ssl.create_default_context(cafile=certifi.where())
        self.ssl_context.check_hostname = False
        self.ssl_context.verify_mode = ssl.CERT_NONE  # For speed (use with caution)
        self.ssl_context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')

        # OPTIMIZED: Aggressive connection pool settings for maximum performance
        self.connector_config = {
            'limit': max_connections,
            'limit_per_host': max_connections // 2,
            'ttl_dns_cache': 600,  # OPTIMIZED: 10 minutes DNS cache
            'use_dns_cache': True,
            'keepalive_timeout': 60,  # OPTIMIZED: Longer keepalive
            'enable_cleanup_closed': True,
            'force_close': False,
            'ssl': self.ssl_context
            # Note: tcp_keepalive, sock_connect, sock_read removed for compatibility
        }

        # OPTIMIZED: Request batching for bulk operations
        self.batch_queue = asyncio.Queue(maxsize=100)
        self.batch_processor_running = False
        self.batch_size = 10
        self.batch_timeout = 0.05  # 50ms batch timeout

        # Initialize session (will be done lazily when needed)
        self._session_initialized = False
    
    async def _initialize_session(self):
        """Initialize HTTP session with optimized settings"""
        if self._session_initialized:
            return

        connector = aiohttp.TCPConnector(**self.connector_config)

        # OPTIMIZED: More aggressive timeout settings
        timeout = aiohttp.ClientTimeout(
            total=self.timeout,
            connect=self.timeout / 3,  # OPTIMIZED: Faster connect timeout
            sock_read=self.timeout / 3,  # OPTIMIZED: Faster read timeout
            sock_connect=self.timeout / 4  # OPTIMIZED: Fastest socket connect
        )

        # OPTIMIZED: Enhanced headers for maximum performance
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': 'AutoGPT-Trader/1.0',
                'Connection': 'keep-alive',
                'Accept-Encoding': 'gzip, deflate, br',  # OPTIMIZED: Added brotli
                'Cache-Control': 'no-cache',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            # OPTIMIZED: JSON serializer optimization
            json_serialize=lambda obj: json.dumps(obj, separators=(',', ':'))
        )

        # OPTIMIZED: Start batch processor for bulk operations
        if not self.batch_processor_running:
            asyncio.create_task(self._batch_processor())
            self.batch_processor_running = True

        self._session_initialized = True
        logger.info(f"✅ [POOL] High-speed connection pool initialized (max: {self.max_connections}, timeout: {self.timeout}s)")
    
    @fast_api_call
    async def make_request(self, method: str, url: str, headers: Optional[Dict] = None, 
                          data: Optional[Dict] = None, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make high-speed HTTP request"""
        if not self.session:
            await self._initialize_session()
        
        start_time = time.time()
        endpoint = url.split('/')[-1] if '/' in url else url
        
        try:
            # Update stats
            if endpoint not in self.connection_stats:
                self.connection_stats[endpoint] = ConnectionStats()
            
            stats = self.connection_stats[endpoint]
            stats.total_requests += 1
            stats.last_used = datetime.now()
            
            # Make request with circuit breaker
            circuit_breaker = speed_optimizer.get_circuit_breaker(f"api_{endpoint}")
            
            async def _make_request():
                async with self.session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=f"HTTP {response.status}"
                        )
            
            result = circuit_breaker.call(_make_request)
            if asyncio.iscoroutine(result):
                result = await result
            
            # Update success stats
            response_time = (time.time() - start_time) * 1000
            stats.successful_requests += 1
            stats.avg_response_time = (
                (stats.avg_response_time * (stats.successful_requests - 1) + response_time) /
                stats.successful_requests
            )
            
            return result
            
        except Exception as e:
            # Update failure stats
            stats.failed_requests += 1
            stats.connection_errors += 1

            logger.warning(f"⚠️ [POOL] Request failed: {method} {url} - {e}")
            raise

    async def bulk_request(self, requests: List[Dict], max_concurrent: int = 10) -> List[Dict]:
        """OPTIMIZED: Execute multiple requests concurrently with controlled concurrency"""
        semaphore = asyncio.Semaphore(max_concurrent)

        async def _execute_request(request_data):
            async with semaphore:
                try:
                    return await self.make_request(
                        method=request_data.get('method', 'GET'),
                        url=request_data['url'],
                        headers=request_data.get('headers'),
                        data=request_data.get('data'),
                        params=request_data.get('params'),
                        endpoint=request_data.get('endpoint', 'bulk')
                    )
                except Exception as e:
                    return {'error': str(e), 'request': request_data}

        # Execute all requests concurrently
        tasks = [_execute_request(req) for req in requests]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        return results

    async def add_to_batch(self, method: str, url: str, headers: Dict = None,
                          data: Dict = None, params: Dict = None, callback: callable = None):
        """OPTIMIZED: Add request to batch queue for processing"""
        try:
            await self.batch_queue.put((method, url, headers, data, params, callback))
        except asyncio.QueueFull:
            # If queue is full, process immediately
            await self.make_request(method, url, headers, data, params, "immediate")
    
    async def create_websocket_connection(self, url: str, on_message: Callable, 
                                        exchange_name: str) -> bool:
        """Create high-speed WebSocket connection"""
        try:
            # Close existing connection if any
            if exchange_name in self.websocket_connections:
                await self._close_websocket(exchange_name)
            
            # Create new connection with speed optimizations
            websocket = await websockets.connect(
                url,
                ssl=self.ssl_context,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=5,
                max_size=2**20,  # 1MB max message size
                compression=None  # Disable compression for speed
            )
            
            self.websocket_connections[exchange_name] = {
                'websocket': websocket,
                'url': url,
                'on_message': on_message,
                'connected_at': datetime.now(),
                'message_count': 0,
                'last_message': None
            }
            
            # Start message handler
            asyncio.create_task(self._handle_websocket_messages(exchange_name))
            
            logger.info(f"✅ [POOL] WebSocket connected: {exchange_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [POOL] WebSocket connection failed: {exchange_name} - {e}")
            return False
    
    async def _handle_websocket_messages(self, exchange_name: str):
        """Handle WebSocket messages with speed optimization"""
        connection_info = self.websocket_connections.get(exchange_name)
        if not connection_info:
            return
        
        websocket = connection_info['websocket']
        on_message = connection_info['on_message']
        
        try:
            async for message in websocket:
                try:
                    # Parse message quickly
                    if isinstance(message, str):
                        data = json.loads(message)
                    else:
                        data = message
                    
                    # Update stats
                    connection_info['message_count'] += 1
                    connection_info['last_message'] = datetime.now()
                    
                    # Handle message asynchronously for speed
                    asyncio.create_task(on_message(data))
                    
                except json.JSONDecodeError:
                    logger.warning(f"⚠️ [POOL] Invalid JSON from {exchange_name}")
                except Exception as e:
                    logger.error(f"❌ [POOL] Message handling error: {exchange_name} - {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.warning(f"⚠️ [POOL] WebSocket disconnected: {exchange_name}")
        except Exception as e:
            logger.error(f"❌ [POOL] WebSocket error: {exchange_name} - {e}")
        finally:
            # Clean up connection
            if exchange_name in self.websocket_connections:
                del self.websocket_connections[exchange_name]
    
    async def _close_websocket(self, exchange_name: str):
        """Close WebSocket connection"""
        if exchange_name in self.websocket_connections:
            try:
                websocket = self.websocket_connections[exchange_name]['websocket']
                await websocket.close()
            except Exception as e:
                logger.warning(f"⚠️ [POOL] Error closing WebSocket: {exchange_name} - {e}")
            finally:
                del self.websocket_connections[exchange_name]
    
    async def send_websocket_message(self, exchange_name: str, message: Dict[str, Any]) -> bool:
        """Send message via WebSocket"""
        if exchange_name not in self.websocket_connections:
            logger.warning(f"⚠️ [POOL] No WebSocket connection: {exchange_name}")
            return False
        
        try:
            websocket = self.websocket_connections[exchange_name]['websocket']
            await websocket.send(json.dumps(message))
            return True
        except Exception as e:
            logger.error(f"❌ [POOL] WebSocket send error: {exchange_name} - {e}")
            return False
    
    def get_connection_health(self) -> Dict[str, Any]:
        """Get connection pool health status"""
        total_requests = sum(stats.total_requests for stats in self.connection_stats.values())
        total_successful = sum(stats.successful_requests for stats in self.connection_stats.values())
        total_failed = sum(stats.failed_requests for stats in self.connection_stats.values())
        
        success_rate = (total_successful / total_requests * 100) if total_requests > 0 else 0
        
        # WebSocket status
        websocket_status = {}
        for name, info in self.websocket_connections.items():
            websocket_status[name] = {
                'connected': True,
                'uptime_seconds': (datetime.now() - info['connected_at']).total_seconds(),
                'message_count': info['message_count'],
                'last_message_ago': (
                    (datetime.now() - info['last_message']).total_seconds()
                    if info['last_message'] else None
                )
            }
        
        # Endpoint performance
        endpoint_performance = {}
        for endpoint, stats in self.connection_stats.items():
            endpoint_performance[endpoint] = {
                'avg_response_time_ms': stats.avg_response_time,
                'success_rate': (stats.successful_requests / stats.total_requests * 100) 
                               if stats.total_requests > 0 else 0,
                'total_requests': stats.total_requests,
                'connection_errors': stats.connection_errors
            }
        
        return {
            'pool_status': 'healthy' if success_rate > 90 else 'degraded' if success_rate > 70 else 'critical',
            'total_requests': total_requests,
            'success_rate': success_rate,
            'active_websockets': len(self.websocket_connections),
            'websocket_status': websocket_status,
            'endpoint_performance': endpoint_performance,
            'session_active': self.session is not None and not self.session.closed
        }
    
    async def health_check(self) -> bool:
        """Perform health check on connection pool"""
        try:
            # Check HTTP session
            if not self.session or self.session.closed:
                await self._initialize_session()
            
            # Check WebSocket connections
            disconnected_connections = []
            for name, info in self.websocket_connections.items():
                websocket = info['websocket']
                if websocket.closed:
                    disconnected_connections.append(name)
            
            # Remove disconnected WebSockets
            for name in disconnected_connections:
                del self.websocket_connections[name]
                logger.warning(f"⚠️ [POOL] Removed disconnected WebSocket: {name}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [POOL] Health check failed: {e}")
            return False
    
    async def _batch_processor(self):
        """OPTIMIZED: Process batched requests for better performance"""
        while True:
            try:
                batch = []
                start_time = time.time()

                # Collect batch items
                while len(batch) < self.batch_size and (time.time() - start_time) < self.batch_timeout:
                    try:
                        item = await asyncio.wait_for(self.batch_queue.get(), timeout=0.01)
                        batch.append(item)
                    except asyncio.TimeoutError:
                        break

                if batch:
                    # Process batch concurrently
                    tasks = [self._process_batch_item(item) for item in batch]
                    await asyncio.gather(*tasks, return_exceptions=True)

                await asyncio.sleep(0.001)  # Minimal sleep to prevent CPU spinning

            except Exception as e:
                logger.error(f"[POOL] Batch processor error: {e}")
                await asyncio.sleep(0.1)

    async def _process_batch_item(self, item):
        """Process individual batch item"""
        try:
            method, url, headers, data, params, callback = item
            result = await self.make_request(method, url, headers, data, params, "batch")
            if callback:
                await callback(result)
        except Exception as e:
            logger.error(f"[POOL] Batch item processing error: {e}")

    async def optimize_connections(self):
        """OPTIMIZED: Enhanced connection pool performance optimization"""
        try:
            # OPTIMIZED: More aggressive idle connection management
            current_time = datetime.now()
            idle_threshold = timedelta(minutes=2)  # OPTIMIZED: Shorter idle threshold

            idle_endpoints = [
                endpoint for endpoint, stats in self.connection_stats.items()
                if stats.last_used and (current_time - stats.last_used) > idle_threshold
            ]

            for endpoint in idle_endpoints:
                # Reset stats for idle endpoints
                self.connection_stats[endpoint] = ConnectionStats()

            # OPTIMIZED: Proactive WebSocket reconnection with health checks
            for name, info in list(self.websocket_connections.items()):
                websocket = info['websocket']
                if websocket.closed or not await self._websocket_health_check(websocket):
                    logger.info(f"🔄 [POOL] Reconnecting WebSocket: {name}")
                    await self.create_websocket_connection(
                        info['url'], info['on_message'], name
                    )

            # OPTIMIZED: Connection pool statistics and auto-tuning
            total_requests = sum(stats.total_requests for stats in self.connection_stats.values())
            if total_requests > 1000:  # Auto-tune after significant usage
                await self._auto_tune_pool()

        except Exception as e:
            logger.error(f"[POOL] Connection optimization error: {e}")

    async def _websocket_health_check(self, websocket) -> bool:
        """OPTIMIZED: Quick WebSocket health check"""
        try:
            await websocket.ping()
            return True
        except:
            return False

    async def _auto_tune_pool(self):
        """OPTIMIZED: Auto-tune connection pool based on performance metrics"""
        try:
            avg_response_times = [stats.avg_response_time for stats in self.connection_stats.values() if stats.avg_response_time > 0]
            if avg_response_times:
                avg_latency = sum(avg_response_times) / len(avg_response_times)

                # Adjust timeout based on observed latency
                if avg_latency > self.timeout * 0.8:  # If we're close to timeout
                    self.timeout = min(self.timeout * 1.2, 1.0)  # Increase timeout but cap at 1s
                    logger.info(f"[POOL] Auto-tuned timeout to {self.timeout:.3f}s")
                elif avg_latency < self.timeout * 0.3:  # If we have headroom
                    self.timeout = max(self.timeout * 0.9, 0.1)  # Decrease timeout but floor at 100ms
                    logger.info(f"[POOL] Auto-tuned timeout to {self.timeout:.3f}s")

        except Exception as e:
            logger.error(f"[POOL] Auto-tuning error: {e}")
            
            logger.debug("🔧 [POOL] Connection optimization completed")
            
        except Exception as e:
            logger.error(f"❌ [POOL] Connection optimization failed: {e}")
    
    async def shutdown(self):
        """Shutdown connection pool"""
        try:
            # Close all WebSocket connections
            for name in list(self.websocket_connections.keys()):
                await self._close_websocket(name)
            
            # Close HTTP session
            if self.session and not self.session.closed:
                await self.session.close()
            
            logger.info("✅ [POOL] Connection pool shutdown completed")
            
        except Exception as e:
            logger.error(f"❌ [POOL] Shutdown error: {e}")

# Global connection pool instance
connection_pool = HighSpeedConnectionPool()

# Convenience functions
async def fast_http_request(method: str, url: str, **kwargs) -> Dict[str, Any]:
    """Make fast HTTP request using connection pool"""
    return await connection_pool.make_request(method, url, **kwargs)

async def create_fast_websocket(url: str, on_message: Callable, exchange_name: str) -> bool:
    """Create fast WebSocket connection"""
    return await connection_pool.create_websocket_connection(url, on_message, exchange_name)

async def send_websocket_message(exchange_name: str, message: Dict[str, Any]) -> bool:
    """Send WebSocket message"""
    return await connection_pool.send_websocket_message(exchange_name, message)
