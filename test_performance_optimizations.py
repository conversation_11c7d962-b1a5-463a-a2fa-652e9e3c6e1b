#!/usr/bin/env python3
"""
PERFORMANCE OPTIMIZATION TEST SUITE
Comprehensive testing of all performance optimizations
"""

import asyncio
import time
import logging
import sys
import os
from typing import Dict, Any

# Add src to path
sys.path.insert(0, 'src')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformanceTestSuite:
    """Comprehensive performance optimization test suite"""
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all performance optimization tests"""
        logger.info("🚀 [TEST] Starting comprehensive performance optimization tests...")
        
        start_time = time.time()
        
        # Test individual components
        await self._test_connection_pool()
        await self._test_intelligent_caching()
        await self._test_parallel_processing()
        await self._test_neural_optimization()
        await self._test_api_optimization()
        await self._test_integration()
        
        # Run performance validation
        await self._test_performance_validation()
        
        total_time = time.time() - start_time
        
        # Generate comprehensive report
        report = self._generate_test_report(total_time)
        
        logger.info(f"✅ [TEST] All performance tests completed in {total_time:.2f}s")
        return report
    
    async def _test_connection_pool(self):
        """Test high-speed connection pool"""
        logger.info("🔗 [TEST] Testing connection pool optimization...")
        
        try:
            from src.exchanges.high_speed_connection_pool import connection_pool
            
            start_time = time.time()
            
            # Initialize connection pool
            await connection_pool._initialize_session()
            
            # Test connection health
            health = connection_pool.get_connection_health()
            
            # Test bulk requests
            requests = [
                {'method': 'GET', 'url': f'https://httpbin.org/delay/0.1?test={i}'}
                for i in range(5)
            ]
            
            bulk_start = time.time()
            results = await connection_pool.bulk_request(requests, max_concurrent=3)
            bulk_time = (time.time() - bulk_start) * 1000
            
            execution_time = (time.time() - start_time) * 1000
            
            self.test_results['connection_pool'] = {
                'status': 'passed',
                'execution_time_ms': execution_time,
                'bulk_request_time_ms': bulk_time,
                'health': health,
                'successful_requests': sum(1 for r in results if not r.get('error', False))
            }
            
            logger.info(f"✅ [TEST] Connection pool test passed: {execution_time:.1f}ms")
            
        except Exception as e:
            self.test_results['connection_pool'] = {
                'status': 'failed',
                'error': str(e)
            }
            logger.error(f"❌ [TEST] Connection pool test failed: {e}")
    
    async def _test_intelligent_caching(self):
        """Test intelligent caching system"""
        logger.info("💾 [TEST] Testing intelligent caching...")
        
        try:
            from src.performance.intelligent_cache_manager import intelligent_cache
            
            start_time = time.time()
            
            # Test cache operations
            test_data = {"test": "data", "timestamp": time.time()}
            
            # Test write
            await intelligent_cache.set("test_key", test_data, category="price")
            
            # Test read
            cached_result = await intelligent_cache.get("test_key")
            
            # Test cache hit
            cached_result_2 = await intelligent_cache.get("test_key")
            
            execution_time = (time.time() - start_time) * 1000
            
            # Get performance report
            cache_report = intelligent_cache.get_performance_report()
            
            self.test_results['intelligent_caching'] = {
                'status': 'passed',
                'execution_time_ms': execution_time,
                'cache_hit': cached_result is not None,
                'cache_hit_2': cached_result_2 is not None,
                'performance_report': cache_report
            }
            
            logger.info(f"✅ [TEST] Intelligent caching test passed: {execution_time:.1f}ms")
            
        except Exception as e:
            self.test_results['intelligent_caching'] = {
                'status': 'failed',
                'error': str(e)
            }
            logger.error(f"❌ [TEST] Intelligent caching test failed: {e}")
    
    async def _test_parallel_processing(self):
        """Test parallel processing system"""
        logger.info("🚀 [TEST] Testing parallel processing...")
        
        try:
            from src.performance.parallel_processor import parallel_processor
            
            start_time = time.time()
            
            # Start parallel processor
            await parallel_processor.start()
            
            # Test parallel execution
            def cpu_task():
                return sum(i * i for i in range(1000))
            
            tasks = [cpu_task for _ in range(8)]
            
            parallel_start = time.time()
            results = await parallel_processor.execute_parallel(
                tasks, task_type="data_processing", timeout=10.0
            )
            parallel_time = (time.time() - parallel_start) * 1000
            
            execution_time = (time.time() - start_time) * 1000
            
            # Get performance report
            perf_report = parallel_processor.get_performance_report()
            
            self.test_results['parallel_processing'] = {
                'status': 'passed',
                'execution_time_ms': execution_time,
                'parallel_execution_time_ms': parallel_time,
                'successful_tasks': sum(1 for r in results if r is not None),
                'total_tasks': len(tasks),
                'performance_report': perf_report
            }
            
            logger.info(f"✅ [TEST] Parallel processing test passed: {execution_time:.1f}ms")
            
        except Exception as e:
            self.test_results['parallel_processing'] = {
                'status': 'failed',
                'error': str(e)
            }
            logger.error(f"❌ [TEST] Parallel processing test failed: {e}")
    
    async def _test_neural_optimization(self):
        """Test neural network optimization"""
        logger.info("🧠 [TEST] Testing neural optimization...")
        
        try:
            from src.neural.performance_optimizer import InferenceOptimizer, OptimizationConfig
            
            start_time = time.time()
            
            # Create optimization config
            config = OptimizationConfig.from_config_file()
            optimizer = InferenceOptimizer(config)
            
            # Test optimization stats
            stats = optimizer.optimization_stats
            
            execution_time = (time.time() - start_time) * 1000
            
            self.test_results['neural_optimization'] = {
                'status': 'passed',
                'execution_time_ms': execution_time,
                'config': {
                    'target_inference_time_ms': config.target_inference_time_ms,
                    'enable_gpu_acceleration': config.enable_gpu_acceleration,
                    'enable_quantization': config.enable_quantization,
                    'precision': config.precision
                },
                'optimization_stats': stats
            }
            
            logger.info(f"✅ [TEST] Neural optimization test passed: {execution_time:.1f}ms")
            
        except Exception as e:
            self.test_results['neural_optimization'] = {
                'status': 'failed',
                'error': str(e)
            }
            logger.error(f"❌ [TEST] Neural optimization test failed: {e}")
    
    async def _test_api_optimization(self):
        """Test API optimization system"""
        logger.info("🌐 [TEST] Testing API optimization...")
        
        try:
            from src.performance.speed_optimizer import speed_optimizer
            
            start_time = time.time()
            
            # Test speed optimizer
            @speed_optimizer.time_operation("test_api", "api", 300.0)
            async def test_api_call():
                await asyncio.sleep(0.05)
                return "success"
            
            result = await test_api_call()
            
            execution_time = (time.time() - start_time) * 1000
            
            # Get performance report
            perf_report = speed_optimizer.get_performance_report()
            
            self.test_results['api_optimization'] = {
                'status': 'passed',
                'execution_time_ms': execution_time,
                'test_result': result,
                'performance_report': perf_report
            }
            
            logger.info(f"✅ [TEST] API optimization test passed: {execution_time:.1f}ms")
            
        except Exception as e:
            self.test_results['api_optimization'] = {
                'status': 'failed',
                'error': str(e)
            }
            logger.error(f"❌ [TEST] API optimization test failed: {e}")
    
    async def _test_integration(self):
        """Test performance integration system"""
        logger.info("⚡ [TEST] Testing performance integration...")
        
        try:
            from src.performance.performance_integration import performance_integrator
            
            start_time = time.time()
            
            # Test initialization
            init_success = await performance_integrator.initialize()
            
            # Get performance stats
            stats = performance_integrator.get_performance_stats()
            
            execution_time = (time.time() - start_time) * 1000
            
            self.test_results['integration'] = {
                'status': 'passed',
                'execution_time_ms': execution_time,
                'initialization_success': init_success,
                'performance_stats': stats
            }
            
            logger.info(f"✅ [TEST] Performance integration test passed: {execution_time:.1f}ms")
            
        except Exception as e:
            self.test_results['integration'] = {
                'status': 'failed',
                'error': str(e)
            }
            logger.error(f"❌ [TEST] Performance integration test failed: {e}")
    
    async def _test_performance_validation(self):
        """Test performance validation system"""
        logger.info("🔍 [TEST] Testing performance validation...")
        
        try:
            from src.performance.performance_validator import performance_validator
            
            start_time = time.time()
            
            # Run validation
            validation_report = await performance_validator.run_comprehensive_validation()
            
            execution_time = (time.time() - start_time) * 1000
            
            self.test_results['performance_validation'] = {
                'status': 'passed',
                'execution_time_ms': execution_time,
                'validation_report': validation_report
            }
            
            logger.info(f"✅ [TEST] Performance validation test passed: {execution_time:.1f}ms")
            
        except Exception as e:
            self.test_results['performance_validation'] = {
                'status': 'failed',
                'error': str(e)
            }
            logger.error(f"❌ [TEST] Performance validation test failed: {e}")
    
    def _generate_test_report(self, total_time: float) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        passed_tests = sum(1 for result in self.test_results.values() 
                          if result.get('status') == 'passed')
        total_tests = len(self.test_results)
        
        return {
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'total_execution_time_seconds': total_time
            },
            'test_results': self.test_results,
            'performance_metrics': self.performance_metrics,
            'recommendations': self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> list:
        """Generate performance recommendations"""
        recommendations = []
        
        failed_tests = [name for name, result in self.test_results.items() 
                       if result.get('status') == 'failed']
        
        if not failed_tests:
            recommendations.append("All performance optimization tests passed - system is fully optimized")
        else:
            for test in failed_tests:
                recommendations.append(f"Review and fix {test} optimization")
        
        return recommendations

async def main():
    """Main test function"""
    print("🧪 AutoGPT Trader - Performance Optimization Test Suite")
    print("=" * 60)
    
    test_suite = PerformanceTestSuite()
    
    try:
        # Run all tests
        report = await test_suite.run_all_tests()
        
        # Display results
        print("\n📋 TEST SUMMARY")
        print("-" * 40)
        summary = report['summary']
        print(f"✅ Passed tests: {summary['passed_tests']}/{summary['total_tests']}")
        print(f"📊 Success rate: {summary['success_rate']:.1f}%")
        print(f"⏱️  Total time: {summary['total_execution_time_seconds']:.2f}s")
        
        # Display recommendations
        if report['recommendations']:
            print("\n💡 RECOMMENDATIONS")
            print("-" * 40)
            for i, rec in enumerate(report['recommendations'], 1):
                print(f"{i}. {rec}")
        
        # Save detailed report
        import json
        with open('performance_test_report.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: performance_test_report.json")
        
        if summary['success_rate'] >= 80:
            print("\n🎯 Performance optimization system is working excellently!")
            return True
        else:
            print("\n⚠️ Some performance optimizations need attention.")
            return False
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
