{"summary": {"total_optimizations": 6, "successful_optimizations": 4, "success_rate": 66.66666666666666, "total_time_seconds": 18.47473931312561, "timestamp": 1751221450.8251493}, "baseline_metrics": {"simple_api_call": 16.08133316040039, "memory_allocation": 0.4565715789794922, "cpu_computation": 0.11897087097167969, "file_io": 0.0035762786865234375}, "optimized_metrics": {}, "improvements": {}, "optimization_results": {"connection_pool": {"status": "failed", "error": "TCPConnector.__init__() got an unexpected keyword argument 'tcp_keepalive'"}, "neural": {"status": "optimized", "config": {"target_inference_time_ms": 100.0, "enable_gpu_acceleration": true, "enable_quantization": true, "enable_jit_compilation": true, "precision": "fp16"}, "optimization_time_ms": 0.000476837158203125}, "caching": {"status": "optimized", "cache_time_ms": 0.14495849609375, "cache_hit": true, "performance_report": {"metrics": {"hits": 1, "misses": 0, "evictions": 0, "prefetch_hits": 0, "total_requests": 1, "avg_access_time": 0.005245208740234375, "memory_usage_mb": 4.673004150390625e-05}, "cache_size": 1, "memory_usage_mb": 4.673004150390625e-05, "max_memory_mb": 512.0, "categories": {"price": 1, "balance": 0, "market_data": 0, "neural_inference": 0, "strategy_signal": 0, "default": 0}, "top_accessed_keys": [["test_optimization", 2]]}}, "parallel_processing": {"status": "optimized", "execution_time_ms": 1193.5837268829346, "successful_tasks": 10, "total_tasks": 10, "performance_report": {"metrics": {"total_tasks": 10, "completed_tasks": 10, "failed_tasks": 0, "success_rate": 100.0, "avg_execution_time_ms": 0.0, "throughput_tasks_per_sec": 8.378210640595022}, "system": {"max_workers": 16, "gpu_enabled": false, "gpu_available": false, "cpu_count": 12, "memory_gb": 15.710594177246094}, "queues": {"high_priority": 0, "medium_priority": 0, "low_priority": 0}}}, "api": {"status": "optimized", "test_api_time_ms": 58.747053146362305, "test_result": "success", "performance_report": {"status": "operational", "total_operations": 1, "success_rate": 100.0, "avg_latencies": {"test_api": 58.65740776062012}, "component_health": {"api": {"avg_latency_ms": 58.65740776062012, "health_score": 94.13425922393799, "status": "healthy"}}, "slow_components": [], "recent_alerts": [], "circuit_breaker_status": {}}}, "validation": {"status": "completed", "report": {"summary": {"total_tests": 7, "passed_tests": 3, "success_rate": 42.857142857142854, "avg_performance_score": 8.202021000690017, "avg_improvement_factor": 19.514029753399132, "validation_time_seconds": 1.5076992511749268}, "targets": {"signal_generation_ms": 500.0, "order_execution_ms": 1000.0, "balance_validation_ms": 100.0, "neural_inference_ms": 200.0, "api_calls_ms": 300.0, "strategy_evaluation_ms": 150.0, "overall_latency_ms": 2000.0}, "system_metrics": {"cpu_count": 12, "cpu_percent_baseline": 42.0, "memory_total_gb": 15.710594177246094, "memory_available_gb": 2.2363128662109375, "memory_percent": 85.8, "timestamp": "2025-06-29T20:24:10.334947"}, "baseline_metrics": {"simple_calculation": 0.03266334533691406, "memory_allocation": 0.46062469482421875, "file_io": 0.029802322387695312}, "test_results": [{"test_name": "API Performance", "target_ms": 300.0, "actual_ms": Infinity, "passed": false, "improvement_factor": 1.0, "error_message": "TCPConnector.__init__() got an unexpected keyword argument 'tcp_keepalive'"}, {"test_name": "<PERSON><PERSON>", "target_ms": 10.0, "actual_ms": Infinity, "passed": false, "improvement_factor": 1.0, "error_message": "'hit_rate'"}, {"test_name": "Memory Usage", "target_ms": 80.0, "actual_ms": 4.545454545454546, "passed": false, "improvement_factor": 0.045454545454545456, "error_message": null}, {"test_name": "Connection Pool", "target_ms": 200.0, "actual_ms": 4.598855972290039, "passed": true, "improvement_factor": 43.4890766758256, "error_message": null}, {"test_name": "Parallel Processing", "target_ms": 1.3431310653686523, "actual_ms": 76.2028694152832, "passed": false, "improvement_factor": 0.03525145642610867, "error_message": null}, {"test_name": "Neural Inference", "target_ms": 200.0, "actual_ms": 19.144344329833984, "passed": true, "improvement_factor": 10.446949582301748, "error_message": null}, {"test_name": "Trading Pipeline", "target_ms": 2000.0, "actual_ms": 434.21030044555664, "passed": true, "improvement_factor": 4.606063002070052, "error_message": null}], "recommendations": ["Consider increasing connection pool size or implementing request batching", "Optimize cache TTL settings or increase cache size", "Implement memory pooling or optimize data structures", "Increase worker count or optimize task distribution"]}}}, "recommendations": ["Review connection pool configuration and network settings"]}