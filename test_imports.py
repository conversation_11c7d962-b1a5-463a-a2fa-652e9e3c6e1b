#!/usr/bin/env python3
"""
Test script to verify all imports work correctly
"""

import sys
import os
from pathlib import Path

print("Testing imports...")
print(f"Python executable: {sys.executable}")
print(f"Python path: {sys.path[:3]}...")

try:
    print("1. Testing pandas...")
    import pandas as pd
    print(f"   ✅ Pandas {pd.__version__} imported successfully")
except ImportError as e:
    print(f"   ❌ Pandas import failed: {e}")

try:
    print("2. Testing numpy...")
    import numpy as np
    print(f"   ✅ Numpy {np.__version__} imported successfully")
except ImportError as e:
    print(f"   ❌ Numpy import failed: {e}")

try:
    print("3. Testing market_data...")
    from src.data_feeds.market_data import MarketDataAggregator, MarketDataStreamer, AlphaFactory
    print("   ✅ Market data components imported successfully")
except ImportError as e:
    print(f"   ❌ Market data import failed: {e}")

try:
    print("4. Testing data_feeds module...")
    import src.data_feeds
    print("   ✅ Data feeds module imported successfully")
except ImportError as e:
    print(f"   ❌ Data feeds module import failed: {e}")

try:
    print("5. Testing real_time_validator...")
    from src.data_feeds.real_time_validator import RealTimeDataValidator
    print("   ✅ RealTimeDataValidator imported successfully")
except ImportError as e:
    print(f"   ❌ RealTimeDataValidator import failed: {e}")

try:
    print("6. Testing live_data_fetcher...")
    from src.data_feeds.live_data_fetcher import LiveDataFetcher, create_default_fetcher
    print("   ✅ LiveDataFetcher imported successfully")
except ImportError as e:
    print(f"   ❌ LiveDataFetcher import failed: {e}")

print("\nAll import tests completed!")
