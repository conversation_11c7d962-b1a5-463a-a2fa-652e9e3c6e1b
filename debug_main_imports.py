#!/usr/bin/env python3
"""
Debug script to replicate the exact import chain from main.py
"""

import os
import sys
from pathlib import Path

# Replicate the exact path setup from main.py
def setup_x_drive_environment():
    """Setup X drive environment and paths"""
    # Force X drive mode
    os.environ['AUTOGPT_X_DRIVE_MODE'] = 'true'
    
    # Set X drive paths
    X_DRIVE_PROJECT = Path("X:/autogpt_trade_project/The_real_deal/autogpt-trader")
    X_DRIVE_SRC = X_DRIVE_PROJECT / "src"
    
    # Clear only specific conflicting paths, preserve pip and conda paths
    paths_to_remove = []
    for p in sys.path:
        p_lower = p.lower()
        # Only remove specific problematic paths, not all E: drive paths
        if any(p_lower.startswith(pattern) for pattern in [
            'e:\\autogpt', 'e:/autogpt', 'e:\\the_real_deal', 'e:/the_real_deal'
        ]):
            paths_to_remove.append(p)

    for path in paths_to_remove:
        sys.path.remove(path)
    
    # Add X drive paths with highest priority
    x_drive_paths = [str(X_DRIVE_PROJECT), str(X_DRIVE_SRC)]
    for path in x_drive_paths:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    # Set environment variables
    os.environ["PROJECT_ROOT"] = str(X_DRIVE_PROJECT)
    os.environ["SRC_DIR"] = str(X_DRIVE_SRC)
    os.environ["PYTHONPATH"] = f"{X_DRIVE_SRC};{X_DRIVE_PROJECT};{os.environ.get('PYTHONPATH', '')}"
    
    print(f"[OK] X Drive environment configured")
    print(f"[PATH] Project root: {X_DRIVE_PROJECT}")
    print(f"[PATH] Source dir: {X_DRIVE_SRC}")
    print(f"[PYTHON-PATH] {sys.path[:5]}")
    
    return X_DRIVE_PROJECT, X_DRIVE_SRC

# Setup X drive environment
PROJECT_ROOT, SRC_DIR = setup_x_drive_environment()

print("\nTesting the exact import chain from main.py...")

try:
    print("1. Testing pandas directly...")
    import pandas as pd
    print(f"   ✅ Pandas {pd.__version__} imported successfully")
except ImportError as e:
    print(f"   ❌ Pandas import failed: {e}")
    sys.exit(1)

try:
    print("2. Testing src.data_feeds.market_data...")
    from src.data_feeds.market_data import MarketDataAggregator, MarketDataStreamer, AlphaFactory
    print("   ✅ Market data components imported successfully")
except ImportError as e:
    print(f"   ❌ Market data import failed: {e}")
    import traceback
    traceback.print_exc()

try:
    print("3. Testing src.data_feeds.__init__...")
    import src.data_feeds
    print("   ✅ Data feeds module imported successfully")
except ImportError as e:
    print(f"   ❌ Data feeds module import failed: {e}")
    import traceback
    traceback.print_exc()

try:
    print("4. Testing RealTimeDataValidator...")
    from src.data_feeds.real_time_validator import RealTimeDataValidator
    print("   ✅ RealTimeDataValidator imported successfully")
except ImportError as e:
    print(f"   ❌ RealTimeDataValidator import failed: {e}")
    import traceback
    traceback.print_exc()

try:
    print("5. Testing neural components...")
    from src.neural.enterprise_memory_system import EnterpriseNeuralMemorySystem
    from src.neural.web_research_integration import WebResearchIntegration
    print("   ✅ Neural components imported successfully")
except ImportError as e:
    print(f"   ❌ Neural components import failed: {e}")
    import traceback
    traceback.print_exc()

try:
    print("6. Testing monitoring components...")
    from src.monitoring.endless_loop_validator import EndlessLoopValidator
    print("   ✅ Monitoring components imported successfully")
except ImportError as e:
    print(f"   ❌ Monitoring components import failed: {e}")
    import traceback
    traceback.print_exc()

print("\n✅ All imports completed successfully!")
print("The issue is not with the imports themselves.")
