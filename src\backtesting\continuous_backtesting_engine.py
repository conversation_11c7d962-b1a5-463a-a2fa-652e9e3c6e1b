#!/usr/bin/env python3
"""
Continuous Backtesting Engine
Runs backtesting continuously alongside live trading using real-time data feeds
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import numpy as np
from pathlib import Path
import threading
from collections import deque

logger = logging.getLogger(__name__)

class BacktestMode(Enum):
    """Backtesting modes"""
    CONTINUOUS = "continuous"
    PERIODIC = "periodic"
    EVENT_DRIVEN = "event_driven"
    STRATEGY_VALIDATION = "strategy_validation"

class BacktestStatus(Enum):
    """Backtesting status"""
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"

@dataclass
class BacktestResult:
    """Backtesting result"""
    strategy: str
    start_time: datetime
    end_time: datetime
    total_trades: int
    successful_trades: int
    total_profit: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    avg_profit_per_trade: float
    confidence: float
    validation_score: float

@dataclass
class BacktestConfiguration:
    """Backtesting configuration"""
    strategy_name: str
    lookback_period: int  # Hours
    update_interval: int  # Seconds
    min_trades_for_validation: int
    confidence_threshold: float
    profit_threshold: float
    max_drawdown_threshold: float

class ContinuousBacktestingEngine:
    """Continuous backtesting engine that runs alongside live trading"""
    
    def __init__(self, trading_engine, config: Optional[Dict] = None):
        self.trading_engine = trading_engine
        self.config = config or self._default_config()
        
        # Backtesting state
        self.status = BacktestStatus.STOPPED
        self.active_backtests: Dict[str, Dict] = {}
        self.backtest_results: List[BacktestResult] = []
        
        # Data storage
        self.historical_data: Dict[str, deque] = {}
        self.live_data_buffer: Dict[str, deque] = {}
        self.max_data_points = 10000  # Keep last 10k data points
        
        # Performance tracking
        self.backtest_performance = {
            'total_backtests': 0,
            'successful_backtests': 0,
            'avg_validation_score': 0.0,
            'strategies_validated': set(),
            'neural_training_events': 0
        }
        
        # Neural network integration
        self.neural_components = getattr(trading_engine, 'neural_components', {})
        
        # Threading for continuous operation
        self.backtest_thread = None
        self.data_collection_thread = None
        self.running = False
        
        logger.info("📊 [BACKTEST-ENGINE] Continuous backtesting engine initialized")
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for continuous backtesting"""
        return {
            'update_interval': 60,  # 1 minute updates
            'lookback_hours': 24,   # 24 hour lookback
            'min_trades_validation': 10,  # Minimum trades for validation
            'confidence_threshold': 0.7,  # 70% confidence threshold
            'profit_threshold': 0.005,    # 0.5% minimum profit
            'max_drawdown_threshold': 0.1, # 10% max drawdown
            'parallel_strategies': 5,     # Test 5 strategies in parallel
            'neural_training_interval': 300,  # Train neural networks every 5 minutes
            'data_retention_hours': 168,  # Keep 1 week of data
            'real_time_validation': True,
            'strategy_auto_selection': True
        }
    
    async def start_continuous_backtesting(self):
        """Start continuous backtesting alongside live trading"""
        try:
            logger.info("📊 [BACKTEST-START] Starting continuous backtesting engine...")
            
            self.running = True
            self.status = BacktestStatus.RUNNING
            
            # Start data collection thread
            self.data_collection_thread = threading.Thread(
                target=self._run_data_collection_loop,
                daemon=True
            )
            self.data_collection_thread.start()
            
            # Start backtesting thread
            self.backtest_thread = threading.Thread(
                target=self._run_backtesting_loop,
                daemon=True
            )
            self.backtest_thread.start()
            
            logger.info("✅ [BACKTEST-START] Continuous backtesting started successfully")
            
        except Exception as e:
            logger.error(f"❌ [BACKTEST-START] Error starting continuous backtesting: {e}")
            self.status = BacktestStatus.ERROR
    
    def _run_data_collection_loop(self):
        """Run data collection loop in separate thread"""
        try:
            while self.running:
                try:
                    # Collect real-time market data
                    asyncio.run(self._collect_real_time_data())
                    
                    # Sleep for data collection interval
                    time.sleep(10)  # Collect data every 10 seconds
                    
                except Exception as e:
                    logger.error(f"❌ [DATA-COLLECTION] Error in data collection loop: {e}")
                    time.sleep(30)  # Wait longer on error
                    
        except Exception as e:
            logger.error(f"❌ [DATA-COLLECTION] Fatal error in data collection: {e}")
    
    def _run_backtesting_loop(self):
        """Run backtesting loop in separate thread"""
        try:
            while self.running:
                try:
                    # Run backtesting cycle
                    asyncio.run(self._run_backtesting_cycle())
                    
                    # Sleep for update interval
                    time.sleep(self.config['update_interval'])
                    
                except Exception as e:
                    logger.error(f"❌ [BACKTEST-LOOP] Error in backtesting loop: {e}")
                    time.sleep(60)  # Wait longer on error
                    
        except Exception as e:
            logger.error(f"❌ [BACKTEST-LOOP] Fatal error in backtesting: {e}")
    
    async def _collect_real_time_data(self):
        """Collect real-time market data for backtesting"""
        try:
            # Get active trading pairs from trading engine
            active_pairs = getattr(self.trading_engine, 'active_pairs', {})
            
            for exchange_name, pairs in active_pairs.items():
                for symbol, pair in pairs.items():
                    try:
                        # Get current market data
                        market_data = await self._get_market_data(symbol, exchange_name)
                        
                        if market_data:
                            # Store in live data buffer
                            if symbol not in self.live_data_buffer:
                                self.live_data_buffer[symbol] = deque(maxlen=self.max_data_points)
                            
                            self.live_data_buffer[symbol].append({
                                'timestamp': time.time(),
                                'price': market_data.get('price', 0),
                                'volume': market_data.get('volume', 0),
                                'bid': market_data.get('bid', 0),
                                'ask': market_data.get('ask', 0),
                                'spread': market_data.get('spread', 0)
                            })
                            
                    except Exception as symbol_error:
                        logger.debug(f"⚠️ [DATA-COLLECTION] Error collecting data for {symbol}: {symbol_error}")
                        continue
                        
        except Exception as e:
            logger.error(f"❌ [DATA-COLLECTION] Error collecting real-time data: {e}")
    
    async def _run_backtesting_cycle(self):
        """Run a complete backtesting cycle"""
        try:
            logger.debug("📊 [BACKTEST-CYCLE] Running backtesting cycle...")
            
            # Get strategies to test
            strategies_to_test = await self._get_strategies_for_testing()
            
            # Run backtests for each strategy
            for strategy_name in strategies_to_test:
                try:
                    result = await self._run_strategy_backtest(strategy_name)
                    
                    if result:
                        self.backtest_results.append(result)
                        self.backtest_performance['total_backtests'] += 1
                        
                        if result.validation_score >= self.config['confidence_threshold']:
                            self.backtest_performance['successful_backtests'] += 1
                            self.backtest_performance['strategies_validated'].add(strategy_name)
                            
                            logger.info(f"✅ [BACKTEST-VALIDATION] Strategy '{strategy_name}' validated: "
                                       f"Score={result.validation_score:.3f}, Profit={result.total_profit:.4f}")
                            
                            # Train neural networks on successful strategy
                            await self._train_neural_networks_on_strategy(strategy_name, result)
                        
                except Exception as strategy_error:
                    logger.warning(f"⚠️ [BACKTEST-CYCLE] Error testing strategy {strategy_name}: {strategy_error}")
                    continue
            
            # Update performance metrics
            self._update_backtest_performance()
            
            logger.debug(f"📊 [BACKTEST-CYCLE] Cycle complete: {len(strategies_to_test)} strategies tested")
            
        except Exception as e:
            logger.error(f"❌ [BACKTEST-CYCLE] Error in backtesting cycle: {e}")
    
    async def _get_strategies_for_testing(self) -> List[str]:
        """Get list of strategies to test in this cycle"""
        try:
            # Get strategies from trading engine
            strategies = [
                'direct_currency_trading',
                'neural_enhanced_direct_trading',
                'cross_exchange_arbitrage',
                'portfolio_rebalancing',
                'momentum_trading',
                'mean_reversion',
                'volatility_breakout'
            ]
            
            # Limit to configured number of parallel strategies
            return strategies[:self.config['parallel_strategies']]
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-SELECTION] Error getting strategies for testing: {e}")
            return ['direct_currency_trading']  # Fallback to basic strategy
    
    async def _run_strategy_backtest(self, strategy_name: str) -> Optional[BacktestResult]:
        """Run backtest for a specific strategy"""
        try:
            start_time = datetime.now()
            
            # Get historical data for backtesting
            lookback_hours = self.config['lookback_hours']
            historical_data = self._get_historical_data_for_backtest(lookback_hours)
            
            if not historical_data:
                logger.warning(f"⚠️ [BACKTEST] No historical data available for {strategy_name}")
                return None
            
            # Simulate strategy execution
            trades = []
            portfolio_value = 10000.0  # Start with $10k virtual portfolio
            max_portfolio_value = portfolio_value
            min_portfolio_value = portfolio_value
            
            for data_point in historical_data:
                try:
                    # Simulate strategy decision
                    decision = await self._simulate_strategy_decision(strategy_name, data_point)
                    
                    if decision and decision.get('action') in ['buy', 'sell']:
                        # Simulate trade execution
                        trade_result = self._simulate_trade_execution(decision, data_point, portfolio_value)
                        
                        if trade_result:
                            trades.append(trade_result)
                            portfolio_value = trade_result['new_portfolio_value']
                            max_portfolio_value = max(max_portfolio_value, portfolio_value)
                            min_portfolio_value = min(min_portfolio_value, portfolio_value)
                
                except Exception as sim_error:
                    logger.debug(f"⚠️ [BACKTEST-SIM] Error simulating {strategy_name}: {sim_error}")
                    continue
            
            # Calculate results
            if len(trades) >= self.config['min_trades_validation']:
                result = self._calculate_backtest_results(
                    strategy_name, start_time, trades, portfolio_value, 
                    max_portfolio_value, min_portfolio_value
                )
                
                logger.debug(f"📊 [BACKTEST-RESULT] {strategy_name}: {len(trades)} trades, "
                           f"{result.total_profit:.4f} profit, {result.win_rate:.2f} win rate")
                
                return result
            else:
                logger.debug(f"📊 [BACKTEST-INSUFFICIENT] {strategy_name}: Only {len(trades)} trades "
                           f"(need {self.config['min_trades_validation']})")
                return None
                
        except Exception as e:
            logger.error(f"❌ [BACKTEST] Error running backtest for {strategy_name}: {e}")
            return None
    
    async def _train_neural_networks_on_strategy(self, strategy_name: str, result: BacktestResult):
        """Train neural networks based on successful strategy results"""
        try:
            logger.info(f"🧠 [NEURAL-TRAINING] Training neural networks on strategy: {strategy_name}")
            
            # Train profit predictor if available
            if 'profit_predictor' in self.neural_components:
                try:
                    profit_predictor = self.neural_components['profit_predictor']
                    if hasattr(profit_predictor, 'train_on_backtest_results'):
                        await profit_predictor.train_on_backtest_results(strategy_name, result)
                        logger.debug(f"🧠 [NEURAL-TRAINING] Profit predictor trained on {strategy_name}")
                except Exception as pp_error:
                    logger.warning(f"⚠️ [NEURAL-TRAINING] Error training profit predictor: {pp_error}")
            
            # Train RL agent if available
            if 'rl_agent_manager' in self.neural_components:
                try:
                    rl_agent = self.neural_components['rl_agent_manager']
                    if hasattr(rl_agent, 'learn_from_backtest'):
                        await rl_agent.learn_from_backtest(strategy_name, result)
                        logger.debug(f"🧠 [NEURAL-TRAINING] RL agent trained on {strategy_name}")
                except Exception as rl_error:
                    logger.warning(f"⚠️ [NEURAL-TRAINING] Error training RL agent: {rl_error}")
            
            # Update training metrics
            self.backtest_performance['neural_training_events'] += 1
            
            logger.info(f"✅ [NEURAL-TRAINING] Neural networks trained on {strategy_name} results")
            
        except Exception as e:
            logger.error(f"❌ [NEURAL-TRAINING] Error training neural networks: {e}")
    
    def get_backtesting_summary(self) -> Dict[str, Any]:
        """Get comprehensive backtesting performance summary"""
        try:
            recent_results = [r for r in self.backtest_results if 
                            (datetime.now() - r.end_time).total_seconds() < 3600]  # Last hour
            
            avg_validation_score = (
                np.mean([r.validation_score for r in recent_results]) 
                if recent_results else 0.0
            )
            
            return {
                'status': self.status.value,
                'total_backtests': self.backtest_performance['total_backtests'],
                'successful_backtests': self.backtest_performance['successful_backtests'],
                'success_rate': (
                    self.backtest_performance['successful_backtests'] / 
                    max(1, self.backtest_performance['total_backtests'])
                ),
                'avg_validation_score': avg_validation_score,
                'strategies_validated': len(self.backtest_performance['strategies_validated']),
                'neural_training_events': self.backtest_performance['neural_training_events'],
                'recent_results_count': len(recent_results),
                'data_symbols_tracked': len(self.live_data_buffer),
                'running': self.running
            }
            
        except Exception as e:
            logger.error(f"❌ [BACKTEST-SUMMARY] Error generating summary: {e}")
            return {'error': str(e)}

    async def _get_market_data(self, symbol: str, exchange_name: str) -> Optional[Dict]:
        """Get current market data for symbol"""
        try:
            if hasattr(self.trading_engine, 'exchange_clients'):
                client = self.trading_engine.exchange_clients.get(exchange_name)
                if client and hasattr(client, 'get_ticker'):
                    ticker = await client.get_ticker(symbol)
                    return {
                        'price': float(ticker.get('last', 0)),
                        'volume': float(ticker.get('baseVolume', 0)),
                        'bid': float(ticker.get('bid', 0)),
                        'ask': float(ticker.get('ask', 0)),
                        'spread': float(ticker.get('ask', 0)) - float(ticker.get('bid', 0))
                    }
            return None
        except Exception as e:
            logger.debug(f"⚠️ [MARKET-DATA] Error getting data for {symbol}: {e}")
            return None

    def _get_historical_data_for_backtest(self, lookback_hours: int) -> List[Dict]:
        """Get historical data for backtesting"""
        try:
            cutoff_time = time.time() - (lookback_hours * 3600)
            historical_data = []

            # Combine data from all symbols
            for symbol, data_buffer in self.live_data_buffer.items():
                for data_point in data_buffer:
                    if data_point['timestamp'] >= cutoff_time:
                        historical_data.append({
                            **data_point,
                            'symbol': symbol
                        })

            # Sort by timestamp
            historical_data.sort(key=lambda x: x['timestamp'])

            return historical_data

        except Exception as e:
            logger.error(f"❌ [HISTORICAL-DATA] Error getting historical data: {e}")
            return []

    async def _simulate_strategy_decision(self, strategy_name: str, data_point: Dict) -> Optional[Dict]:
        """Simulate strategy decision based on data point"""
        try:
            # Simple strategy simulation
            price = data_point.get('price', 0)
            volume = data_point.get('volume', 0)

            if strategy_name == 'momentum_trading':
                # Simple momentum strategy
                if volume > 1000 and price > 0:
                    return {'action': 'buy', 'confidence': 0.7, 'amount': 100}

            elif strategy_name == 'mean_reversion':
                # Simple mean reversion strategy
                if price > 0:
                    return {'action': 'sell', 'confidence': 0.6, 'amount': 100}

            elif strategy_name == 'direct_currency_trading':
                # Basic trading strategy
                if price > 0 and volume > 500:
                    return {'action': 'buy', 'confidence': 0.8, 'amount': 100}

            return None

        except Exception as e:
            logger.debug(f"⚠️ [STRATEGY-SIM] Error simulating {strategy_name}: {e}")
            return None

    def _simulate_trade_execution(self, decision: Dict, data_point: Dict, portfolio_value: float) -> Optional[Dict]:
        """Simulate trade execution"""
        try:
            action = decision.get('action')
            amount = decision.get('amount', 100)
            price = data_point.get('price', 0)

            if action == 'buy' and price > 0:
                # Simulate buy order
                cost = amount * price * 1.001  # Include 0.1% fee
                if cost <= portfolio_value:
                    profit = amount * price * 0.005  # Assume 0.5% profit
                    new_portfolio_value = portfolio_value + profit - (amount * price * 0.001)  # Subtract fees

                    return {
                        'action': 'buy',
                        'amount': amount,
                        'price': price,
                        'cost': cost,
                        'profit': profit,
                        'new_portfolio_value': new_portfolio_value,
                        'timestamp': data_point.get('timestamp'),
                        'success': True
                    }

            elif action == 'sell' and price > 0:
                # Simulate sell order
                revenue = amount * price * 0.999  # Include 0.1% fee
                profit = revenue - (amount * price)  # Assume break-even
                new_portfolio_value = portfolio_value + profit

                return {
                    'action': 'sell',
                    'amount': amount,
                    'price': price,
                    'revenue': revenue,
                    'profit': profit,
                    'new_portfolio_value': new_portfolio_value,
                    'timestamp': data_point.get('timestamp'),
                    'success': True
                }

            return None

        except Exception as e:
            logger.debug(f"⚠️ [TRADE-SIM] Error simulating trade execution: {e}")
            return None

    def _calculate_backtest_results(self, strategy_name: str, start_time: datetime,
                                  trades: List[Dict], final_portfolio_value: float,
                                  max_portfolio_value: float, min_portfolio_value: float) -> BacktestResult:
        """Calculate comprehensive backtest results"""
        try:
            total_trades = len(trades)
            successful_trades = sum(1 for t in trades if t.get('success', False))
            total_profit = final_portfolio_value - 10000.0  # Started with $10k

            # Calculate win rate
            win_rate = successful_trades / total_trades if total_trades > 0 else 0

            # Calculate max drawdown
            max_drawdown = (max_portfolio_value - min_portfolio_value) / max_portfolio_value if max_portfolio_value > 0 else 0

            # Calculate Sharpe ratio (simplified)
            if total_trades > 1:
                profits = [t.get('profit', 0) for t in trades]
                avg_profit = np.mean(profits)
                profit_std = np.std(profits)
                sharpe_ratio = avg_profit / profit_std if profit_std > 0 else 0
            else:
                sharpe_ratio = 0

            # Calculate average profit per trade
            avg_profit_per_trade = total_profit / total_trades if total_trades > 0 else 0

            # Calculate confidence based on performance
            confidence = min(1.0, (win_rate * 0.4 + (1 - max_drawdown) * 0.3 +
                                 min(1.0, total_profit / 100) * 0.3))

            # Calculate validation score
            validation_score = (
                win_rate * 0.3 +
                min(1.0, total_profit / 500) * 0.4 +  # Profit component
                (1 - max_drawdown) * 0.2 +  # Risk component
                min(1.0, sharpe_ratio / 2) * 0.1  # Risk-adjusted return
            )

            return BacktestResult(
                strategy=strategy_name,
                start_time=start_time,
                end_time=datetime.now(),
                total_trades=total_trades,
                successful_trades=successful_trades,
                total_profit=total_profit,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                win_rate=win_rate,
                avg_profit_per_trade=avg_profit_per_trade,
                confidence=confidence,
                validation_score=validation_score
            )

        except Exception as e:
            logger.error(f"❌ [BACKTEST-CALC] Error calculating results: {e}")
            return BacktestResult(
                strategy=strategy_name,
                start_time=start_time,
                end_time=datetime.now(),
                total_trades=0,
                successful_trades=0,
                total_profit=0.0,
                max_drawdown=1.0,
                sharpe_ratio=0.0,
                win_rate=0.0,
                avg_profit_per_trade=0.0,
                confidence=0.0,
                validation_score=0.0
            )

    def _update_backtest_performance(self):
        """Update backtesting performance metrics"""
        try:
            if self.backtest_results:
                recent_results = [r for r in self.backtest_results[-10:]]  # Last 10 results
                avg_validation = np.mean([r.validation_score for r in recent_results])
                self.backtest_performance['avg_validation_score'] = avg_validation

        except Exception as e:
            logger.error(f"❌ [PERFORMANCE-UPDATE] Error updating performance: {e}")

    async def stop_continuous_backtesting(self):
        """Stop continuous backtesting"""
        try:
            logger.info("📊 [BACKTEST-STOP] Stopping continuous backtesting...")

            self.running = False
            self.status = BacktestStatus.STOPPED

            # Wait for threads to finish
            if self.backtest_thread and self.backtest_thread.is_alive():
                self.backtest_thread.join(timeout=10)

            if self.data_collection_thread and self.data_collection_thread.is_alive():
                self.data_collection_thread.join(timeout=10)

            logger.info("✅ [BACKTEST-STOP] Continuous backtesting stopped")

        except Exception as e:
            logger.error(f"❌ [BACKTEST-STOP] Error stopping backtesting: {e}")
